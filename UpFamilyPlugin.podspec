#
#  Be sure to run `pod spec lint UpFamilyPlugin.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |spec|

  # ―――  Spec Metadata  ―――――――――――――――――――――――――――――――――――――――――――――――――――――――――― #

  spec.name         = "UpFamilyPlugin"
  spec.version      = "0.0.2.**********"
  spec.summary      = "UpFamilyPlugin"

  spec.description  = <<-DESC
      UpFamilyPlugin 插件库
                   DESC

  spec.homepage     = "http://**************:8091/a/uplus/plugins/UpFamilyPlugin/ios"
  spec.license         = 'MIT'
  spec.author       = "海尔优家智能科技（北京）有限公司"
  spec.source       = { :git => "https://git.haier.net/uplus/ios/plugins/UpFamilyPlugin.git", :tag => spec.version.to_s}
  spec.ios.deployment_target = '12.0'
  spec.requires_arc    = true
  spec.frameworks      = 'Foundation'
  spec.module_name     = 'UpFamilyPlugin'
  spec.source_files  = "UpFamilyPlugin/*.{h,m}", "UpFamilyPlugin/**/*.{h,m}"
  spec.exclude_files = "Classes/Exclude"
  
  spec.dependency 'UpPluginFoundation', '>= 0.1.13'
  spec.dependency 'UPPluginBaseAPI', '>= 0.1.1'
  spec.dependency "upuserdomain",">=3.7.8"
  spec.dependency 'uplog','>=1.1.22'
  spec.dependency 'MJExtension','>=3.2.1'
  spec.dependency 'upnetwork','>=4.0.1'
  
  spec.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES'
  }

end

