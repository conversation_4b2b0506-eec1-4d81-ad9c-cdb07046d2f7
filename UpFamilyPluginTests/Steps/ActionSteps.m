//
//  ActionSteps.m
//  UpFamilyPluginTests
//
//  Created by 冉东军 on 2021/8/27.
//

#import "ActionSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import <MJExtension/MJExtension.h>
#import "StepsCacheData.h"
#import "UpFamilyPluginManager.h"
#import "StepsUtils.h"
#import "UPUnitTestCallBackIMP.h"
#import <upuserdomain/Room+PrivateExtension.h>
#import <upuserdomain/UDFloorInfo+PrivateExtension.h>


@interface ActionSteps ()
@property (nonatomic, strong) StepsCacheData *stepsCacheData;
@property (nonatomic, strong) NSString *kFamilyId;
@property (nonatomic, strong) NSMutableDictionary<NSString *, id> *mConditionVariableMap;
@property (nonatomic, strong) NSString *kDeviceId;
@end
@implementation ActionSteps
- (instancetype)init
{
    self = [super init];
    if (self) {
        [self defineStepsAndHooks];
    }
    return self;
}

- (void)defineStepsAndHooks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.stepsCacheData = [[StepsCacheData alloc] init];
      self.kFamilyId = nil;
      self.kDeviceId = nil;
      self.mConditionVariableMap = [NSMutableDictionary dictionary];
    });

    void (^ConditionPrepare)(NSDictionary *, NSString *) = ^(NSDictionary *userInfo, NSString *key) {
      NSDictionary *dict = jsonObjectFromEscapedString(userInfo[kDocStringKey]);
      self.mConditionVariableMap[key] = dict;
    };

#pragma mark - Given
    Given(@"^创建基于\"([^\"]*)\"平台的\"([^\"]*)\"的action$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *actionName = args[1];
      CallMethodPlatformType platformType = platformTypeWithString(args[0]);
      self.stepsCacheData.actions[actionName] = ({
          Class cls = [UpPluginActionManager.sharedInstance getActionCreatorWithName:actionName];
          UpPluginAction *action = [cls new];
          action.callerType = platformType;
          action;
      });
    });

    Given(@"^\"([^\"]*)\"的家庭\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kFamilyId = args[0];
      NSString *kFamily = args[1];
      if ([kFamily isEqualToString:@"不存在"]) {
          [OCMStub([[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:kFamilyId]) andReturn:nil];
      }
      else {
          id<UDFamilyDelegate> protocolMockFamily = OCMProtocolMock(@protocol(UDFamilyDelegate));
          [[[(OCMockObject *)UpFamilyPluginManager.sharedInstance.userDomain.user stub] andReturn:protocolMockFamily] getFamilyById:kFamilyId];
      }
      if (![kFamilyId isEqualToString:@"fakeNewFamilyId"]) {
          self.kFamilyId = kFamilyId;
      }

    });

    Given(@"^执行UserDomain的\"([^\"]*)\"接口\"([^\"]*)\",retCode为:\"([^\"]*)\",retInfo为:\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self executeMockMethodWithRetCodeAndRetInfo:args userInfo:userInfo];
    });

    Given(@"^\"([^\"]*)\"的设备\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kDeviceId = args[0];
      NSString *kDevice = args[1];
      if ([kDevice isEqualToString:@"不存在"]) {
          [[[(OCMockObject *)UpFamilyPluginManager.sharedInstance.userDomain.user stub] andReturn:nil] devices];
      }
      else {
          id<UDDeviceDelegate> protocolMockDevice = OCMProtocolMock(@protocol(UDDeviceDelegate));
          [[[(OCMockObject *)protocolMockDevice stub] andReturn:kDeviceId] deviceId];
          [[[(OCMockObject *)UpFamilyPluginManager.sharedInstance.userDomain.user stub] andReturn:protocolMockDevice] getDeviceById:kDeviceId];
      }
      self.kDeviceId = kDeviceId;
    });

    Given(@"^\"([^\"]*)\"的房间\"([^\"]*)\",家庭楼层信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kRoom = args[1];
      NSArray *floorInfos = getFloorInfoArrFromDateTable(userInfo);
      id<UDFamilyDelegate> kfamily = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:self.kFamilyId];
      if ([kRoom isEqualToString:@"不存在"]) {
          [[[(OCMockObject *)kfamily stub] andReturn:nil] floorInfos];
      }
      else {
          [[[(OCMockObject *)kfamily stub] andReturn:floorInfos] floorInfos];
      }
    });

    Given(@"^\"([^\"]*)\"的楼层\"([^\"]*)\",家庭楼层信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *kRoom = args[1];
      NSArray *floorInfos = getFloorInfoArrFromDateTable2(userInfo);
      id<UDFamilyDelegate> kfamily = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:self.kFamilyId];
      if ([kRoom isEqualToString:@"不存在"]) {
          [[[(OCMockObject *)kfamily stub] andReturn:nil] floorInfos];
      }
      else {
          [[[(OCMockObject *)kfamily stub] andReturn:floorInfos] floorInfos];
      }
    });

    Given(@"^设置\"([^\"]*)\"获取家庭列表变量\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[1]);
    });

    Given(@"^设置\"([^\"]*)\"获取当前家庭信息变量\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[1]);
    });

    Given(@"^设置UserDomain用户对象的家庭列表变量为\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^设置UserDomain用户对象的当前家庭信息变量为\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^设置UserDomain用户对象的指定家庭信息变量为\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^Nebula获取指定家庭信息变量\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^Nebula获取家庭第一个成员\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^设置\"([^\"]*)\"批量处理设备部分成功\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[1]);
    });

    Given(@"^设置\"([^\"]*)\"批量处理设备全部成功\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[1]);
    });

    Given(@"^UserDomain创建楼层的变量为\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^虚拟成员加入家庭的变量为\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^修改虚拟成员的变量为\"([^\"]*)\":$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ConditionPrepare(userInfo, args[0]);
    });

    Given(@"^UserDomain登录状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UpUserDomainState loginState = UpUserDomainStateUnLogin;
      if ([args[0] isEqualToString:@"已登录"]) {
          loginState = UpUserDomainStateDidLogin;
      }
      OCMStub([UpFamilyPluginManager.sharedInstance.userDomain state]).andReturn(loginState);
    });

    Given(@"^UserDomain刷新家庭列表状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([UpFamilyPluginManager.sharedInstance.userDomain isRefreshFamilyListCompleted]).andReturn([args[0] isEqualToString:@"已完成"]);
    });

    Given(@"^执行UserDomain的\"([^\"]*)\"接口\"([^\"]*)\",retData如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self executeMockMethodWithRetData:args userInfo:userInfo];
    });

    Given(@"^UserDomain的用户对象执行\"([^\"]*)\"方法,返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      if ([args[0] isEqualToString:@"getFamilies"]) {
          NSDictionary *families = self.mConditionVariableMap[@"FamilyListParam"];
          [[[(OCMockObject *)UpFamilyPluginManager.sharedInstance.userDomain.user stub] andReturn:@{families.allKeys.firstObject : createMockFamily(families.allValues.firstObject)}] families];
      }
      else if ([args[0] isEqualToString:@"getCurrentFamily"]) {
          NSDictionary *familyDict = self.mConditionVariableMap[@"CurrentFamilyParam"];
          [[[(OCMockObject *)UpFamilyPluginManager.sharedInstance.userDomain.user stub] andReturn:createMockFamily(familyDict)] currentFamily];
      }
    });

#pragma mark - When
    When(@"^调用名称为\"([^\"]*)\"的action,入参如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      __weak typeof(self) weakSelf = self;
      NSDictionary *dict = convertParaFrom(userInfo);
      UpPluginAction *action = self.stepsCacheData.actions[args[0]];
      [action execute:args[0]
               params:dict
              options:nil
          finishBlock:[[UPUnitTestCallBackIMP alloc] initWithCallback:^(id _Nonnull retData) {
            weakSelf.stepsCacheData.action2ExecuteResult[args[0]] = retData;
          }]];
    });

#pragma mark - Then
    Then(@"^执行\"([^\"]*)\"失败,retCode为:\"([^\"]*)\",retInfo如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *retCode = args[1];
      NSString *retInfo = convertErrorMessageFrom(userInfo);
      NSString * (^extractErrorParamStr)(NSString *errorInfo) = ^NSString *(NSString *errorInfo)
      {
          NSString *result = [errorInfo stringByReplacingOccurrencesOfString:@"非法参数错误" withString:@""];
          result = [result stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"()"]];
          return result;
      };
      id actualResult = self.stepsCacheData.action2ExecuteResult[args[0]];
      id actualInfoObj = jsonObjectFromEscapedString(extractErrorParamStr(actualResult[@"retInfo"]));
      id expectedInfoObj = jsonObjectFromEscapedString(extractErrorParamStr(retInfo));
      CCIAssert([actualResult[@"retCode"] isEqual:retCode], @"执行%@失败,错误码%@与预期不符", args[0], actualResult[@"retCode"]);
      CCIAssert([actualInfoObj isEqual:expectedInfoObj], @"执行%@失败,错误信息与预期不符", args[0]);
    });

    Then(@"^执行\"([^\"]*)\"失败,retCode为:\"([^\"]*)\",retInfo为:\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *retCode = args[1];
      NSString *retInfo = args[2];
      id actualResult = self.stepsCacheData.action2ExecuteResult[args[0]];
      [self verifyRetCodeWith:retCode and:actualResult[@"retCode"]];
      [self verifyRetCodeWith:retInfo and:actualResult[@"retInfo"]];

    });

    Then(@"^执行\"([^\"]*)\"成功,retCode为:\"([^\"]*)\",retInfo为:\"([^\"]*)\",retData为:\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *retCode = args[1];
      NSString *retInfo = args[2];
      NSString *retData = args[3];
      id actualResult = self.stepsCacheData.action2ExecuteResult[args[0]];
      [self verifyRetCodeWith:retCode and:actualResult[@"retCode"]];
      [self verifyRetCodeWith:retInfo and:actualResult[@"retInfo"]];
      [self verifyRetDataWith:[retData isEqualToString:@"null"] ? nil : retData and:actualResult[@"retData"]];
    });

    Then(@"^执行\"([^\"]*)\"成功,retCode为:\"([^\"]*)\",retInfo为:\"([^\"]*)\",retData如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      id actualResult = self.stepsCacheData.action2ExecuteResult[args[0]];
      id returnData = userInfo[kDocStringKey];
      if (!returnData) {
          NSArray *values = valuesFromDataTable(userInfo);
          returnData = values[0];
      }
      id kTemp = jsonObjectFromEscapedString(returnData);
      id expectedRetData = jsonObjectFromVariableObject(kTemp, self.mConditionVariableMap);
      CCIAssert(areObjectsEqual(actualResult[@"retCode"], args[1]), @"执行%@失败,状态码%@与预期不符", args[1], actualResult[@"retCode"]);
      CCIAssert(areObjectsEqual(actualResult[@"retInfo"], args[2]), @"执行%@失败,状态信息%@与预期不符", args[2], actualResult[@"retInfo"]);
      CCIAssert(isEqualsFamilyList(actualResult[@"retData"], expectedRetData), @"执行%@失败,返回数据%@与预期不符", expectedRetData, actualResult[@"retData"]);
    });
}

#pragma mark - private method
- (void)verifyRetCodeWith:(NSString *)expectRetCode and:(NSString *)actualRetCode
{
    CCIAssert([expectRetCode isEqualToString:actualRetCode], [NSString stringWithFormat:@"执行失败,期望的retCode为:%@,实际为:%@", expectRetCode, actualRetCode]);
}

- (void)verifyRetInfoWith:(NSString *)expectRetInfo and:(NSString *)actuaRetlInfo
{
    expectRetInfo = [[NSMutableString alloc] initWithString:expectRetInfo];
    if ([expectRetInfo containsString:@"\\"]) {
        expectRetInfo = [expectRetInfo stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    }
    CCIAssert([expectRetInfo isEqualToString:actuaRetlInfo], [NSString stringWithFormat:@"执行失败,期望的retInfo为:%@,实际为:%@", expectRetInfo, actuaRetlInfo]);
}

- (void)verifyRetDataWith:(id)expectRetData and:(id)actualRetData
{
    if (expectRetData == nil && actualRetData == nil) {
        CCIAssert(YES, [NSString stringWithFormat:@"执行失败,期望的retData为:%@,实际为:%@", expectRetData, actualRetData]);
    }
    else if ([expectRetData isKindOfClass:[NSArray class]] && [actualRetData isKindOfClass:[NSArray class]]) {
        CCISAssert(isEqualsRoomList(expectRetData, actualRetData), [NSString stringWithFormat:@"执行失败,期望的retData为:%@,实际为:%@", expectRetData, actualRetData]);
    }
    else if ([expectRetData isKindOfClass:[NSDictionary class]] && [actualRetData isKindOfClass:[NSDictionary class]]) {
        CCISAssert([expectRetData[@"roomId"] isEqual:actualRetData[@"roomId"]], [NSString stringWithFormat:@"执行失败,期望的retData为:%@,实际为:%@", expectRetData, actualRetData]);
    }
    else {
        CCIAssert([expectRetData isEqualToString:actualRetData], [NSString stringWithFormat:@"执行失败,期望的retData为:%@,实际为:%@", expectRetData, actualRetData]);
    }
}

- (void)executeMockMethodWithRetCodeAndRetInfo:(NSArray<NSString *> *)args userInfo:(NSDictionary *)userInfo
{
    NSString *actionName = args[0];
    NSString *success = args[1];
    id<UDFamilyDelegate> kFamily = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:self.kFamilyId];
    if ([actionName isEqualToString:@"exitFamilyAsMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:3];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };

        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] exitFamilyAsMemberSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"exitFamilyAsAdmin"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] exitFamilyAsAdmin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"adminDeleteMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] adminDeleteMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"adminInviteMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] adminInvitateMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"deleteFamilyAsAdmin"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:3];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] destoryFamilyAsAdminSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"changeFamilyAdmin"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] changeFamilyAdminUserId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyDeviceName"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        id<UDDeviceDelegate> device = [UpFamilyPluginManager.sharedInstance.userDomain.user getDeviceById:self.kDeviceId];
        [[[(OCMockObject *)device stub] andDo:proxyBlock] updateDeviceName:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyAndCheckDeviceName"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:6];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        id<UDDeviceDelegate> device = [UpFamilyPluginManager.sharedInstance.userDomain.user getDeviceById:self.kDeviceId];
        [[[(OCMockObject *)device stub] andDo:proxyBlock] updateDeviceName:[OCMArg any] checkLevel:[OCMArg any] type:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"editRoomName"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:5];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] updateRoomName:[OCMArg any] roomId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"refreshRoomList"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] queryRoomList:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"createRoom"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] addRoom:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"deleteRoom"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] removeRoom:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"moveDevicesToRoom"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:5];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] moveDevicesToOtherRoom:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"createFamily"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)UpFamilyPluginManager.sharedInstance.userDomain.user stub] andDo:proxyBlock] createFamily:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"createFamilyFloor"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] createFloor:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"editFamilyFloor"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] editFloor:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"deleteFamilyFloor"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] deleteFloor:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"updateFamilyInfo"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] updateFamilyInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"queryFamilyInfo"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:3];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] queryInfoSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"queryFirstMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:3];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] queryFirstMemeberSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"addVirtualMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:5];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] addVirtualMember:[OCMArg any] memberName:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyVirtualMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] modifyVirtualMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"moveDevicesToFamily"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:5];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] moveDevicesToOtherFamily:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"unbindDevices"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] unBindDevices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"removeDevices"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] removeDevices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyMemberRole"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:5];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] modifyMemberRole:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyVirtualMemberRole"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:5];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] modifyVirtualMemberRole:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"saveRoomsOrder"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:5];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] saveRoomsOrder:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"confirmDeviceSharingRelation"]) {
        id<UDUserDelegate> kUser = [UpFamilyPluginManager sharedInstance].userDomain.user;
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDUserDelegate>)[[(OCMockObject *)kUser stub] andDo:proxyBlock]
            confirmDeviceSharingRelation:[OCMArg any]
                                 success:[OCMArg any]
                                 failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"cancelDeviceSharingRelation"]) {
        id<UDUserDelegate> kUser = [UpFamilyPluginManager sharedInstance].userDomain.user;
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *failureBlockPointer;
          [invocation getArgument:&failureBlockPointer atIndex:4];
          void (^failureCallback)(UserDomainSampleResult *result);
          failureCallback = (__bridge void (^)(UserDomainSampleResult *))failureBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retCode = args[2];
          result.retInfo = args[3];
          failureCallback(result);
        };
        [(id<UDUserDelegate>)[[(OCMockObject *)kUser stub] andDo:proxyBlock]
            cancelDeviceSharingRelation:[OCMArg any]
                                success:[OCMArg any]
                                failure:[OCMArg any]];
    }
}

- (void)executeMockMethodWithRetData:(NSArray<NSString *> *)args userInfo:(NSDictionary *)userInfo
{
    NSString *actionName = args[0];
    NSString *success = args[1];
    id<UDFamilyDelegate> kFamily = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:self.kFamilyId];
    NSArray<NSArray<NSString *> *> *dataList = getListsFromDateTable(userInfo);
    NSString *retData = dataList.firstObject[0];
    if ([actionName isEqualToString:@"exitFamilyAsMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:2];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] exitFamilyAsMemberSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"exitFamilyAsAdmin"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] exitFamilyAsAdmin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"adminDeleteMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] adminDeleteMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"adminInviteMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] adminInvitateMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"deleteFamilyAsAdmin"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:2];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] destoryFamilyAsAdminSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"changeFamilyAdmin"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] changeFamilyAdminUserId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyDeviceName"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        id<UDDeviceDelegate> device = [UpFamilyPluginManager.sharedInstance.userDomain.user getDeviceById:self.kDeviceId];
        [[[(OCMockObject *)device stub] andDo:proxyBlock] updateDeviceName:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyAndCheckDeviceName"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:5];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        id<UDDeviceDelegate> device = [UpFamilyPluginManager.sharedInstance.userDomain.user getDeviceById:self.kDeviceId];
        [[[(OCMockObject *)device stub] andDo:proxyBlock] updateDeviceName:[OCMArg any] checkLevel:[OCMArg any] type:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"editRoomName"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:4];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] updateRoomName:[OCMArg any] roomId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"createRoom"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] addRoom:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"deleteRoom"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] removeRoom:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"createFamilyFloor"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          result.retData = returnData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] createFloor:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"editFamilyFloor"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] editFloor:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"deleteFamilyFloor"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] deleteFloor:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"updateFamilyInfo"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] updateFamilyInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"refreshRoomList"]) {
        NSArray *retData = getRoomListFromDateTable(userInfo);
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        id<UDFamilyDelegate> kFamily = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:self.kFamilyId];
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] queryRoomList:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"createFamily"]) {
        //      NSArray<NSArray<NSString *> *> *dataList = getListsFromDateTable(userInfo);
        //      NSString *familyId = dataList.firstObject[0];
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [[[(OCMockObject *)UpFamilyPluginManager.sharedInstance.userDomain.user stub] andDo:proxyBlock] createFamily:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"moveDevicesToRoom"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:4];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          BatchProcessDeviceResult *deviceResult = [BatchProcessDeviceResult mj_objectWithKeyValues:returnData];
          result.retData = deviceResult;
          successCallback(result);
        };
        id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:self.kFamilyId];
        [[[(OCMockObject *)family stub] andDo:proxyBlock] moveDevicesToOtherRoom:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"queryFamilyInfo"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:2];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          id<UDFamilyDelegate> familyResult = createMockFamily(returnData);
          result.retData = familyResult;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] queryInfoSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"queryFirstMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:2];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          id<UDFamilyMemberDelegate> memberResult = createMockFamilyMember(returnData);
          result.retData = memberResult;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] queryFirstMemeberSuccess:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"addVirtualMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:4];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          result.retData = returnData;
          successCallback(result);
        };
        [[[(OCMockObject *)kFamily stub] andDo:proxyBlock] addVirtualMember:[OCMArg any] memberName:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyVirtualMember"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          result.retData = returnData;
          successCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] modifyVirtualMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"moveDevicesToFamily"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:4];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          BatchProcessDeviceResult *deviceResult = [BatchProcessDeviceResult mj_objectWithKeyValues:returnData];
          result.retData = deviceResult;
          successCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] moveDevicesToOtherFamily:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"unbindDevices"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          BatchProcessDeviceResult *deviceResult = [BatchProcessDeviceResult mj_objectWithKeyValues:returnData];
          result.retData = deviceResult;
          successCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] unBindDevices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"removeDevices"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          id returnData = jsonObjectFromVariableObject(retData, self.mConditionVariableMap);
          BatchProcessDeviceResult *deviceResult = [BatchProcessDeviceResult mj_objectWithKeyValues:returnData];
          result.retData = deviceResult;
          successCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] removeDevices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyMemberRole"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:4];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] modifyMemberRole:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"modifyVirtualMemberRole"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:4];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] modifyVirtualMemberRole:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"saveRoomsOrder"]) {
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:4];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [(id<UDFamilyDelegate>)[[(OCMockObject *)kFamily stub] andDo:proxyBlock] saveRoomsOrder:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"confirmDeviceSharingRelation"]) {
        id<UDUserDelegate> kUser = [UpFamilyPluginManager sharedInstance].userDomain.user;
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [(id<UDUserDelegate>)[[(OCMockObject *)kUser stub] andDo:proxyBlock]
            confirmDeviceSharingRelation:[OCMArg any]
                                 success:[OCMArg any]
                                 failure:[OCMArg any]];
    }
    else if ([actionName isEqualToString:@"cancelDeviceSharingRelation"]) {
        id<UDUserDelegate> kUser = [UpFamilyPluginManager sharedInstance].userDomain.user;
        void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
          [invocation retainArguments];
          void *successBlockPointer;
          [invocation getArgument:&successBlockPointer atIndex:3];
          void (^successCallback)(UserDomainSampleResult *result);
          successCallback = (__bridge void (^)(UserDomainSampleResult *))successBlockPointer;
          UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
          result.success = NO;
          if ([success isEqualToString:@"成功"]) {
              result.success = YES;
          }
          result.retData = retData;
          successCallback(result);
        };
        [(id<UDUserDelegate>)[[(OCMockObject *)kUser stub] andDo:proxyBlock]
            cancelDeviceSharingRelation:[OCMArg any]
                                success:[OCMArg any]
                                failure:[OCMArg any]];
    }
}

@end
