//
//  InitSteps.m
//  UpFamilyPluginTests
//
//  Created by 冉东军 on 2021/9/6.
//

#import "InitSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "UpFamilyPluginManager.h"
#import <upuserdomain/User.h>
#import "UpPluginFamilyTestHolder.h"

@interface InitSteps ()

@end

@implementation InitSteps
- (instancetype)init
{
    self = [super init];
    if (self) {
        [self defineStepsAndHooks];
    }
    return self;
}

- (void)defineStepsAndHooks
{
    before(^(CCIScenarioDefinition *scenario) {
      [UpPluginFamilyTestHolder shareHolder].manager = nil;

    });

    Given(@"^UpPluginFamilyManager已经初始化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      if ([UpPluginFamilyTestHolder shareHolder].manager) {
          return;
      }
      [UpPluginFamilyTestHolder shareHolder].manager = OCMPartialMock([UpFamilyPluginManager sharedInstance]);
    });

    Given(@"^UserDomain对象使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *param = args[0];
      if ([param isEqualToString:@"模拟的"]) {
          UpUserDomain *userDomain = OCMClassMock([UpUserDomain class]);
          OCMStub([[UpPluginFamilyTestHolder shareHolder].manager userDomain]).andReturn(userDomain);
      }
    });

    Given(@"^UserDomain的用户对象使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *param = args[0];
      if ([param isEqualToString:@"模拟的"]) {
          id<UDUserDelegate> protocolMockUser = OCMProtocolMock(@protocol(UDUserDelegate));
          OCMStub([[UpFamilyPluginManager sharedInstance].userDomain user]).andReturn(protocolMockUser);
      }
    });
}
@end
