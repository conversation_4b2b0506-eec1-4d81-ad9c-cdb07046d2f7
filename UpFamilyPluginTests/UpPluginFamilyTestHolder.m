//
//  UpPluginFamilyTestHolder.m
//  UpFamilyPluginTests
//
//  Created by 冉东军 on 2021/9/9.
//

#import "UpPluginFamilyTestHolder.h"

@implementation UpPluginFamilyTestHolder
+ (UpPluginFamilyTestHolder *)shareHolder
{
    static UpPluginFamilyTestHolder *holder = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      holder = [[UpPluginFamilyTestHolder alloc] init];
    });
    return holder;
}
@end
