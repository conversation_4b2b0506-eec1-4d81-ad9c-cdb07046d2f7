//
//  NSDictionary+SubMap.m
//  UpStoragePluginTests
//
//  Created by 张虎 on 2021/6/1.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "NSDictionary+SubMap.h"

@implementation NSDictionary (SubMap)

- (BOOL)isSubDictionaryOfDict:(NSDictionary *)dict
{
    BOOL __block result = YES;
    [self enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
      if (![obj isEqual:dict[key]]) {
          result = NO;
          *stop = YES;
      }
    }];
    return result;
}

@end
