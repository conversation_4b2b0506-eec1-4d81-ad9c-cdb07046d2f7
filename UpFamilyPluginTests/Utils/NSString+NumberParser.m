//
//  NSString+NumberParser.m
//  UpStoragePluginTests
//
//  Created by 张虎 on 2021/5/29.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "NSString+NumberParser.h"

@implementation NSString (NumberParser)

- (double)toDouble
{
    double result = [self doubleValue];
    NSScanner *scanner = [NSScanner scannerWithString:self];
    [scanner scanDouble:&result];
    return result;
}

- (double)toFloat
{
    float result = [self floatValue];
    NSScanner *scanner = [NSScanner scannerWithString:self];
    [scanner scanFloat:&result];
    return result;
}

@end
