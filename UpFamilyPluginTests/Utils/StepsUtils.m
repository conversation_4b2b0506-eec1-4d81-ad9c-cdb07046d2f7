//
//  StepsUtils.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepsUtils.h"
#import "NSString+NumberParser.h"
#import <OCMock/OCMock.h>
#import <upuserdomain/Room+PrivateExtension.h>
#import <upuserdomain/UDFloorInfo+PrivateExtension.h>

NSString *const kNil = @"空对象";
NSString *const kNilStr = @"";


id _Nullable objectFromStepStringArg(NSString *stringArg)
{
    NSString *str = [stringArg stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;

    if ([str hasPrefix:@"*B*"]) {
        str = [str stringByReplacingOccurrencesOfString:@"*B*" withString:@""];
    }

    //    if ([str isEqualToString:@"null"]) {
    //        result = nil;
    //    }
    else if ([str isEqualToString:@"\"\""]) {
        result = @"";
    }
    else if (([@[ @"true", @"yes" ] containsObject:str.lowercaseString])) {
        result = @(YES);
    }
    else if ([@[ @"false", @"no" ] containsObject:str.lowercaseString]) {
        result = @(NO);
    }
    else if ([str hasPrefix:@"*N*"]) {
        str = [str stringByReplacingOccurrencesOfString:@"*N*" withString:@""];
        result = nil;
    }
    else if ([str hasPrefix:@"*D*"]) {
        str = [str stringByReplacingOccurrencesOfString:@"*D*" withString:@""];
        if ([str containsString:@"."]) {
            result = @([str toDouble]);
        }
        else {
            result = @(str.integerValue);
        }
    }
    return result;
}


id jsonObjectFromEscapedString(NSString *escapedString)
{
    if ([escapedString hasPrefix:@"*S*"]) {
        return [[escapedString stringByReplacingOccurrencesOfString:@"*S*" withString:@""] stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    }

    id nonJsonObj = objectFromStepStringArg(escapedString);
    if (!nonJsonObj) {
        return nil;
    }
    if (nonJsonObj && (![nonJsonObj isKindOfClass:NSString.class] || [nonJsonObj length] == 0)) {
        return nonJsonObj;
    }

    NSString *str = [escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;
    if (([str hasPrefix:@"{"] && [str hasSuffix:@"}"]) ||
        ([str hasPrefix:@"["] && [str hasSuffix:@"]"])) {
        result = [NSJSONSerialization JSONObjectWithData:[[escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""] dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingFragmentsAllowed error:NULL];
    }
    return result;
}

CallMethodPlatformType platformTypeWithString(NSString *str)
{
    CallMethodPlatformType platformType = 0;
    if ([str compare:@"flutter" options:NSCaseInsensitiveSearch] == NSOrderedSame) {
        platformType = CallMethodPlatformFlutter;
    }
    else {
        platformType = CallMethodPlatformH5;
    }
    return platformType;
}


BOOL areObjectsEqual(id obj1, id obj2)
{
    if ([obj1 isKindOfClass:NSDictionary.class] && [obj1 count] == 0) {
        obj1 = nil;
    }
    if ([obj2 isKindOfClass:NSDictionary.class] && [obj2 count] == 0) {
        obj2 = nil;
    }
    if (!obj1 && !obj2) {
        return YES;
    }
    return [obj1 isEqual:obj2];
}


id jsonObjectFromVariableObject(id escapedJson, NSDictionary *variableJson)
{
    if ([escapedJson isKindOfClass:NSString.class]) {
        __block NSDictionary *result = nil;
        [variableJson enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
          if ([escapedJson isEqualToString:key]) {
              result = jsonObjectFromVariableObject(obj, variableJson);
          }
        }];

        return result ?: escapedJson;
    }
    else if ([escapedJson isKindOfClass:NSDictionary.class]) {
        NSMutableDictionary *kReplacedJson = [NSMutableDictionary dictionaryWithDictionary:escapedJson];
        NSDictionary *kReplacedJsonCopy = [kReplacedJson copy];
        [kReplacedJsonCopy enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
          if ([obj isKindOfClass:NSString.class]) {
              id data = [variableJson objectForKey:obj];
              data ? [kReplacedJson setObject:data forKey:key] : nil;
          }
          else {
              [kReplacedJson setObject:jsonObjectFromVariableObject(obj, variableJson) forKey:key];
          }
        }];
        return kReplacedJson;
    }
    else if ([escapedJson isKindOfClass:NSArray.class]) {
        NSMutableArray *kReplacedJson = [NSMutableArray arrayWithArray:escapedJson];
        NSArray *kReplacedJsonCopy = [kReplacedJson copy];
        [kReplacedJsonCopy enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if ([obj isKindOfClass:NSString.class]) {
              id data = [variableJson objectForKey:obj];
              data ? [kReplacedJson replaceObjectAtIndex:idx withObject:data] : nil;
          }
          else {
              [kReplacedJson replaceObjectAtIndex:idx withObject:jsonObjectFromVariableObject(obj, variableJson)];
          }
        }];
        return kReplacedJson;
    }

    return escapedJson;
}


NSArray *valuesFromDataTable(NSDictionary *userInfo)
{
    if (!userInfo) {
        return nil;
    }

    NSArray *arr = userInfo[@"DataTable"];
    if (arr.count < 2) {
        return nil;
    }
    return arr[1];
}

NSArray<NSArray<NSString *> *> *getListsFromDateTable(NSDictionary *expectAttrTable)
{
    NSMutableArray<NSArray<NSString *> *> *expect = expectAttrTable[@"DataTable"];
    NSMutableArray *result = expect.mutableCopy;
    [result removeObjectAtIndex:0];
    return result;
}

id convertParaFrom(NSDictionary *expectParaTable)
{
    NSArray<NSArray<NSString *> *> *dataList = getListsFromDateTable(expectParaTable);
    NSArray<NSString *> *paraList = dataList.firstObject;
    NSString *jsonStr = paraList[0];
    if (!jsonStr) {
        return nil;
    }
    NSData *jsonData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    id result = [NSJSONSerialization JSONObjectWithData:jsonData ?: [NSData new]
                                                options:NSJSONReadingMutableContainers
                                                  error:&err];
    if (err) {
        NSLog(@"json解析失败：%@", err);
        return nil;
    }
    return result;
}

NSString *convertErrorMessageFrom(NSDictionary *expectParaTable)
{
    NSArray<NSArray<NSString *> *> *dataList = getListsFromDateTable(expectParaTable);
    NSArray<NSString *> *paraList = dataList.firstObject;
    NSString *jsonStr = paraList[0];
    return jsonStr;
}
BOOL isEqualString(NSString *str1, NSString *str2)
{
    if (str1 == str2) {
        return YES;
    }
    if (str1 != nil) {
        return [str1 isEqualToString:str2];
    }
    return NO;
}
BOOL isEqualsRoomData(NSDictionary *actualRoom, NSDictionary *expectRoom)
{
    if (actualRoom == expectRoom) {
        return true;
    }
    if (actualRoom != nil && expectRoom != nil) {
        return isEqualString(actualRoom[@"roomId"], expectRoom[@"roomId"]) && isEqualString(actualRoom[@"roomName"], expectRoom[@"roomName"]);
    }
    return false;
}

BOOL isEqualsRoomList(NSArray *actualArray, NSArray *expectArray)
{
    if (actualArray == expectArray) {
        return true;
    }

    if (actualArray.count != expectArray.count) {
        return false;
    }

    NSMutableArray *temp = [NSMutableArray arrayWithArray:[actualArray copy]];
    [expectArray enumerateObjectsUsingBlock:^(NSDictionary *_Nonnull obj2, NSUInteger idx, BOOL *_Nonnull stop) {
      [temp enumerateObjectsUsingBlock:^(NSDictionary *_Nonnull obj1, NSUInteger idx, BOOL *_Nonnull stop) {
        if (isEqualsRoomData(obj1, obj2)) {
            [temp removeObject:obj1];
        }
      }];
    }];
    if (temp.count == 0) {
        return YES;
    }
    return false;
}

id<UDFamilyDelegate> createMockFamily(NSDictionary *familyDict)
{
    id<UDFamilyDelegate> mockFamily = OCMProtocolMock(@protocol(UDFamilyDelegate));
    OCMStub([mockFamily createTime]).andReturn(familyDict[@"createTime"]);
    OCMStub([mockFamily familyId]).andReturn(familyDict[@"familyId"]);
    id<UDFamilyMemberDelegate> mockFirstMember = OCMProtocolMock(@protocol(UDFamilyMemberDelegate));
    OCMStub([mockFirstMember joinTime]).andReturn(familyDict[@"firstMember"][@"joinTime"]);
    OCMStub([mockFirstMember familyId]).andReturn(familyDict[@"firstMember"][@"familyId"]);
    OCMStub([mockFirstMember memberName]).andReturn(familyDict[@"firstMember"][@"memberName"]);
    OCMStub([mockFirstMember shareDeviceCount]).andReturn([familyDict[@"firstMember"][@"shareDeviceCount"] integerValue]);
    OCMStub([mockFirstMember memberRole]).andReturn(familyDict[@"firstMember"][@"memberRole"]);
    id<UDMemberInfoDelegate> mockMemberInfo = OCMProtocolMock(@protocol(UDMemberInfoDelegate));
    OCMStub([mockMemberInfo avatarUrl]).andReturn(familyDict[@"firstMember"][@"memberInfo"][@"avatarUrl"]);
    OCMStub([mockMemberInfo mobile]).andReturn(familyDict[@"firstMember"][@"memberInfo"][@"mobile"]);
    OCMStub([mockMemberInfo name]).andReturn(familyDict[@"firstMember"][@"memberInfo"][@"name"]);
    OCMStub([mockMemberInfo userId]).andReturn(familyDict[@"firstMember"][@"memberInfo"][@"userId"]);
    OCMStub([mockMemberInfo virtualUserFlag]).andReturn([familyDict[@"firstMember"][@"memberInfo"][@"virtualUserFlag"] boolValue]);
    OCMStub([mockMemberInfo ucUserId]).andReturn(familyDict[@"firstMember"][@"memberInfo"][@"ucUserId"]);
    OCMStub([mockMemberInfo hostUserId]).andReturn(familyDict[@"firstMember"][@"memberInfo"][@"hostUserId"]);
    OCMStub([mockMemberInfo birthday]).andReturn(familyDict[@"firstMember"][@"memberInfo"][@"birthday"]);
    OCMStub([mockFirstMember memberInfo]).andReturn(mockMemberInfo);
    OCMStub([mockFamily firstMember]).andReturn(mockFirstMember);
    id<UDFamilyInfoDelegate> mockInfo = OCMProtocolMock(@protocol(UDFamilyInfoDelegate));
    OCMStub([mockInfo familyName]).andReturn(familyDict[@"info"][@"familyName"]);
    OCMStub([mockInfo familyPosition]).andReturn(familyDict[@"info"][@"familyPosition"]);
    id<UDFamilyLocationDelegate> mockFamilyLocation = OCMProtocolMock(@protocol(UDFamilyLocationDelegate));
    OCMStub([mockFamilyLocation cityCode]).andReturn(familyDict[@"info"][@"familyLocation"][@"cityCode"]);
    OCMStub([mockFamilyLocation latitude]).andReturn(familyDict[@"info"][@"familyLocation"][@"latitude"]);
    OCMStub([mockFamilyLocation longitude]).andReturn(familyDict[@"info"][@"familyLocation"][@"longitude"]);
    OCMStub([mockInfo familyLocation]).andReturn(mockFamilyLocation);
    OCMStub([mockFamily info]).andReturn(mockInfo);
    OCMStub([mockFamily locationChangeFlag]).andReturn([familyDict[@"locationChangeFlag"] boolValue]);
    NSMutableArray<id<UDFamilyMemberDelegate>> *mockMembers = [NSMutableArray array];
    [(NSArray *)(familyDict[@"members"]) enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      id<UDFamilyMemberDelegate> mockMember = OCMProtocolMock(@protocol(UDFamilyMemberDelegate));
      OCMStub([mockMember joinTime]).andReturn(obj[@"joinTime"]);
      OCMStub([mockMember familyId]).andReturn(obj[@"familyId"]);
      OCMStub([mockMember memberName]).andReturn(obj[@"memberName"]);
      OCMStub([mockMember memberRole]).andReturn(obj[@"memberRole"]);
      OCMStub([mockMember shareDeviceCount]).andReturn([obj[@"shareDeviceCount"] integerValue]);
      id<UDMemberInfoDelegate> mockMemberInfo2 = OCMProtocolMock(@protocol(UDMemberInfoDelegate));
      OCMStub([mockMemberInfo2 avatarUrl]).andReturn(obj[@"memberInfo"][@"avatarUrl"]);
      OCMStub([mockMemberInfo2 mobile]).andReturn(obj[@"memberInfo"][@"mobile"]);
      OCMStub([mockMemberInfo2 name]).andReturn(obj[@"memberInfo"][@"name"]);
      OCMStub([mockMemberInfo2 userId]).andReturn(obj[@"memberInfo"][@"userId"]);
      OCMStub([mockMemberInfo2 virtualUserFlag]).andReturn([obj[@"memberInfo"][@"virtualUserFlag"] boolValue]);
      OCMStub([mockMemberInfo2 ucUserId]).andReturn(obj[@"memberInfo"][@"ucUserId"]);
      OCMStub([mockMemberInfo2 hostUserId]).andReturn(obj[@"memberInfo"][@"hostUserId"]);
      OCMStub([mockMemberInfo2 birthday]).andReturn(obj[@"memberInfo"][@"birthday"]);
      OCMStub([mockMember memberInfo]).andReturn(mockMemberInfo2);
      [mockMembers addObject:mockMember];
    }];
    OCMStub([mockFamily members]).andReturn(mockMembers);
    id<UDMemberInfoDelegate> mockOwner = OCMProtocolMock(@protocol(UDMemberInfoDelegate));
    OCMStub([mockOwner avatarUrl]).andReturn(familyDict[@"owner"][@"avatarUrl"]);
    OCMStub([mockOwner mobile]).andReturn(familyDict[@"owner"][@"mobile"]);
    OCMStub([mockOwner name]).andReturn(familyDict[@"owner"][@"name"]);
    OCMStub([mockOwner userId]).andReturn(familyDict[@"owner"][@"userId"]);
    OCMStub([mockOwner virtualUserFlag]).andReturn([familyDict[@"owner"][@"virtualUserFlag"] boolValue]);
    OCMStub([mockOwner ucUserId]).andReturn(familyDict[@"owner"][@"ucUserId"]);
    OCMStub([mockFamily owner]).andReturn(mockOwner);
    OCMStub([mockFamily ownerId]).andReturn(familyDict[@"ownerId"]);
    NSMutableArray<id<UDFloorInfoDelegate>> *mockFloors = [NSMutableArray array];
    [(NSArray *)(familyDict[@"floorInfos"]) enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      id<UDFloorInfoDelegate> mockFloorInfo = OCMProtocolMock(@protocol(UDFloorInfoDelegate));
      OCMStub([mockFloorInfo floorId]).andReturn(obj[@"floorId"]);
      OCMStub([mockFloorInfo floorOrderId]).andReturn(obj[@"floorOrderId"]);
      OCMStub([mockFloorInfo floorName]).andReturn(obj[@"floorName"]);
      OCMStub([mockFloorInfo floorClass]).andReturn(obj[@"floorClass"]);
      OCMStub([mockFloorInfo floorLabel]).andReturn(obj[@"floorLabel"]);
      OCMStub([mockFloorInfo floorLogo]).andReturn(obj[@"floorLogo"]);
      OCMStub([mockFloorInfo floorPicture]).andReturn(obj[@"floorPicture"]);
      OCMStub([mockFloorInfo floorCreateTime]).andReturn(obj[@"floorCreateTime"]);
      NSMutableArray<id<UDRoomDelegate>> *mockRooms = [NSMutableArray array];
      [(NSArray *)(obj[@"rooms"]) enumerateObjectsUsingBlock:^(id _Nonnull obj1, NSUInteger idx1, BOOL *_Nonnull stop1) {
        id<UDRoomDelegate> mockRoom = OCMProtocolMock(@protocol(UDRoomDelegate));
        OCMStub([mockRoom roomId]).andReturn(obj1[@"roomId"]);
        OCMStub([mockRoom roomName]).andReturn(obj1[@"roomName"]);
        OCMStub([mockRoom roomClass]).andReturn(obj1[@"roomClass"]);
        OCMStub([mockRoom roomLabel]).andReturn(obj1[@"roomLabel"]);
        OCMStub([mockRoom roomLogo]).andReturn(obj1[@"roomLogo"]);
        OCMStub([mockRoom roomPicture]).andReturn(obj1[@"roomPicture"]);
        [mockRooms addObject:mockRoom];
      }];
      OCMStub([mockFloorInfo rooms]).andReturn(mockRooms);
      [mockFloors addObject:mockFloorInfo];
    }];
    OCMStub([mockFamily floorInfos]).andReturn(mockFloors);
    NSMutableArray<id<UDDeviceDelegate>> *mockShareDevices = [NSMutableArray array];
    [(NSArray *)(familyDict[@"sharedDevices"]) enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      id<UDDeviceDelegate> mockShareDevice = OCMProtocolMock(@protocol(UDDeviceDelegate));
      OCMStub([mockShareDevice familyId]).andReturn(familyDict[@"familyId"]);
      OCMStub([mockShareDevice apptypeName]).andReturn(obj[@"apptypeName"]);
      OCMStub([mockShareDevice brand]).andReturn(obj[@"brand"]);
      OCMStub([mockShareDevice deviceId]).andReturn(obj[@"deviceId"]);
      OCMStub([mockShareDevice deviceName]).andReturn(obj[@"deviceName"]);
      OCMStub([mockShareDevice deviceType]).andReturn(obj[@"deviceType"]);
      OCMStub([mockShareDevice deviceRole]).andReturn(obj[@"deviceRole"]);
      OCMStub([mockShareDevice deviceRoleType]).andReturn(obj[@"deviceRoleType"]);
      OCMStub([mockShareDevice imageAddr1]).andReturn(obj[@"imageAddr1"]);
      OCMStub([mockShareDevice imageAddr2]).andReturn(obj[@"imageAddr2"]);
      OCMStub([mockShareDevice isOnline]).andReturn([obj[@"isOnline"] boolValue]);
      OCMStub([mockShareDevice model]).andReturn(obj[@"model"]);
      OCMStub([mockShareDevice wifiType]).andReturn(obj[@"wifiType"]);
      OCMStub([mockShareDevice prodNo]).andReturn(obj[@"prodNo"]);
      OCMStub([mockShareDevice bindType]).andReturn(obj[@"bindType"]);
      OCMStub([mockShareDevice bindTime]).andReturn(obj[@"bindTime"]);
      OCMStub([mockShareDevice comunicationMode]).andReturn(obj[@"comunicationMode"]);
      OCMStub([mockShareDevice ownerId]).andReturn(obj[@"ownerId"]);
      OCMStub([mockShareDevice devFloorId]).andReturn(obj[@"devFloorId"]);
      OCMStub([mockShareDevice devFloorOrderId]).andReturn(obj[@"devFloorOrderId"]);
      OCMStub([mockShareDevice devFloorName]).andReturn(obj[@"devFloorName"]);
      OCMStub([mockShareDevice deviceNetType]).andReturn(obj[@"deviceNetType"]);
      OCMStub([mockShareDevice noKeepAlive]).andReturn([obj[@"noKeepAlive"] integerValue]);
      id<UDDeviceOwnerInfoDelegate> mockDeviceOwnerInfo = OCMProtocolMock(@protocol(UDDeviceOwnerInfoDelegate));
      OCMStub([mockDeviceOwnerInfo mobile]).andReturn(obj[@"ownerInfo"][@"mobile"]);
      OCMStub([mockDeviceOwnerInfo userId]).andReturn(obj[@"ownerInfo"][@"userId"]);
      OCMStub([mockShareDevice ownerInfo]).andReturn(mockDeviceOwnerInfo);
      id<UDRoomDelegate> mockDeviceRoom = OCMProtocolMock(@protocol(UDRoomDelegate));
      OCMStub([mockDeviceRoom roomId]).andReturn(obj[@"room"][@"roomId"]);
      OCMStub([mockDeviceRoom roomName]).andReturn(obj[@"room"][@"roomName"]);
      OCMStub([mockShareDevice room]).andReturn(mockDeviceRoom);
      id<UDDevicePermissionDelegate> mockDevicePermission = OCMProtocolMock(@protocol(UDDevicePermissionDelegate));
      OCMStub([mockDevicePermission authType]).andReturn(obj[@"permission"][@"authType"]);
      id<UDDeviceAuthDelegate> mockDeviceAuth = OCMProtocolMock(@protocol(UDDeviceAuthDelegate));
      OCMStub([mockDeviceAuth control]).andReturn([obj[@"permission"][@"auth"][@"control"] boolValue]);
      OCMStub([mockDeviceAuth set]).andReturn([obj[@"permission"][@"auth"][@"set"] boolValue]);
      OCMStub([mockDeviceAuth view]).andReturn([obj[@"permission"][@"auth"][@"view"] boolValue]);
      OCMStub([mockDevicePermission auth]).andReturn(mockDeviceAuth);
      OCMStub([mockShareDevice permission]).andReturn(mockDevicePermission);
      [mockShareDevices addObject:mockShareDevice];
    }];
    OCMStub([mockFamily getDeviceList]).andReturn(mockShareDevices);
    return mockFamily;
}

id<UDFamilyMemberDelegate> createMockFamilyMember(NSDictionary *memberDict)
{
    id<UDFamilyMemberDelegate> mockMember = OCMProtocolMock(@protocol(UDFamilyMemberDelegate));
    OCMStub([mockMember memberRole]).andReturn(memberDict[@"memberRole"]);
    OCMStub([mockMember joinTime]).andReturn(memberDict[@"joinTime"]);
    OCMStub([mockMember memberName]).andReturn(memberDict[@"memberName"]);
    OCMStub([mockMember shareDeviceCount]).andReturn([memberDict[@"shareDeviceCount"] integerValue]);
    id<UDMemberInfoDelegate> mockMemberInfo2 = OCMProtocolMock(@protocol(UDMemberInfoDelegate));
    OCMStub([mockMemberInfo2 avatarUrl]).andReturn(memberDict[@"memberInfo"][@"avatarUrl"]);
    OCMStub([mockMemberInfo2 mobile]).andReturn(memberDict[@"memberInfo"][@"mobile"]);
    OCMStub([mockMemberInfo2 name]).andReturn(memberDict[@"memberInfo"][@"name"]);
    OCMStub([mockMemberInfo2 userId]).andReturn(memberDict[@"memberInfo"][@"userId"]);
    OCMStub([mockMemberInfo2 virtualUserFlag]).andReturn([memberDict[@"memberInfo"][@"virtualUserFlag"] boolValue]);
    OCMStub([mockMemberInfo2 ucUserId]).andReturn(memberDict[@"memberInfo"][@"ucUserId"]);
    OCMStub([mockMemberInfo2 hostUserId]).andReturn(memberDict[@"memberInfo"][@"hostUserId"]);
    OCMStub([mockMemberInfo2 birthday]).andReturn(memberDict[@"memberInfo"][@"birthday"]);
    OCMStub([mockMember memberInfo]).andReturn(mockMemberInfo2);
    OCMStub([mockMember familyId]).andReturn(memberDict[@"familyId"]);
    return mockMember;
}

NSArray *getRoomListFromDateTable(NSDictionary *userInfo)
{
    NSArray *dicArr = convertParaFrom(userInfo);
    NSMutableArray *retData = [NSMutableArray array];
    for (NSDictionary *dict in dicArr) {
        Room *room = [[Room alloc] init];
        room.realRoomId = dict[@"roomId"];
        room.realRoomName = dict[@"roomName"];
        [retData addObject:room];
    }
    return retData;
}

NSArray *getFloorInfoArrFromDateTable(NSDictionary *userInfo)
{
    NSArray *floorInfoArr = convertParaFrom(userInfo);
    NSMutableArray *floorInfos = [NSMutableArray array];
    for (NSDictionary *dict in floorInfoArr) {
        UDFloorInfo *floorInfo = [[UDFloorInfo alloc] init];
        floorInfo.realFloorName = dict[@"floorName"];
        floorInfo.realFloorId = dict[@"floorId"];
        floorInfo.realFloorOrderId = dict[@"floorOrderId"];
        NSArray *rooms = dict[@"rooms"];
        NSMutableArray *roomArray = [NSMutableArray array];
        for (NSDictionary *dict2 in rooms) {
            Room *room = [[Room alloc] init];
            room.realRoomId = dict2[@"roomId"];
            [roomArray addObject:room];
        }
        floorInfo.realRooms = roomArray;
        [floorInfos addObject:floorInfo];
    }
    return floorInfos;
}

NSArray *getFloorInfoArrFromDateTable2(NSDictionary *userInfo)
{
    NSArray *floorInfoArr = convertParaFrom(userInfo);
    NSMutableArray *floorInfos = [NSMutableArray array];
    for (NSDictionary *dict in floorInfoArr) {
        UDFloorInfo *floorInfo = [[UDFloorInfo alloc] init];
        floorInfo.realFloorName = dict[@"floorName"];
        floorInfo.realFloorId = dict[@"floorId"];
        floorInfo.realFloorOrderId = dict[@"floorOrderId"];
        [floorInfos addObject:floorInfo];
    }
    return floorInfos;
}

BOOL isEqualsFamilyList(id obj1, id obj2)
{
    if ([obj1 isEqual:[NSNull null]]) {
        obj1 = nil;
    }
    if ([obj2 isEqual:[NSNull null]]) {
        obj2 = nil;
    }
    if ([obj1 isKindOfClass:[NSString class]] && [obj1 isEqualToString:@"null"]) {
        obj1 = nil;
    }
    if ([obj2 isKindOfClass:[NSString class]] && [obj2 isEqualToString:@"null"]) {
        obj2 = nil;
    }
    if ([obj1 isKindOfClass:NSDictionary.class] && [obj1 count] == 0) {
        obj1 = nil;
    }
    if ([obj2 isKindOfClass:NSDictionary.class] && [obj2 count] == 0) {
        obj2 = nil;
    }
    if (!obj1 && !obj2) {
        return YES;
    }
    if ([obj1 isEqual:obj2]) {
        return YES;
    }
    return NO;
}
