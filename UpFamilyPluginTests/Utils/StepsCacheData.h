//
//  StepsCacheData.h
//  UpStoragePluginTests
//
//  Created by 张虎 on 2021/5/25.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UpPluginFoundation/UpPluginFoundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface StepsCacheData : NSObject

@property (nonatomic, nullable) UpPluginAction *action;
@property (nonatomic, nullable) id executeResult;

@property (nonatomic) NSMutableDictionary<NSString *, UpPluginAction *> *actions;
@property (nonatomic) NSMutableDictionary<NSString *, id> *action2ExecuteResult;

@end

NS_ASSUME_NONNULL_END
