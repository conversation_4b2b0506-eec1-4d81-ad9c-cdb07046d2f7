//
//  UPUnitTestCallBackIMP.m
//  UpStoragePluginTests
//
//  Created by 韩波标 on 2021/5/14.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPUnitTestCallBackIMP.h"

@interface UPUnitTestCallBackIMP ()

@property (nonatomic, strong) void (^callback)(id retData);

@end

@implementation UPUnitTestCallBackIMP
- (instancetype)initWithCallback:(void (^)(id _Nonnull))callbackObject
{
    if (self = [super init]) {
        self.callback = callbackObject;
    }
    return self;
}

- (void)onSuccess:(id)retData
{
    if (self.callback) {
        self.callback(retData);
    }
}

- (void)onFailure:(NSString *)errorCode errMessage:(NSString *)errorMessage details:(id)errorDetails
{
    if (self.callback) {
        self.callback(errorDetails);
    }
}
@end
