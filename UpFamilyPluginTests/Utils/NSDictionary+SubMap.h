//
//  NSDictionary+SubMap.h
//  UpStoragePluginTests
//
//  Created by 张虎 on 2021/6/1.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSDictionary (SubMap)

/// 判断 receiver 是否指定字典的子字典。如果字典 A 中的所有键值对都包含在字典 B 中，那么 A 是 B 的子字典。
/// @param dict 指定字典
- (BOOL)isSubDictionaryOfDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
