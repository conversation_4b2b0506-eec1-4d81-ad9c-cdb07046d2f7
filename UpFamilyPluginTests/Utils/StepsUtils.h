//
//  StepsUtils.h
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UpDevice;

/// 将含有转义字符的 json 字符串转成 json 对象
id jsonObjectFromEscapedString(NSString *escapedString);

id _Nullable objectFromStepStringArg(NSString *formalString);

CallMethodPlatformType platformTypeWithString(NSString *str);

/// 比较 action 的两个执行返回值是否相等
BOOL areActionResultsEqual(NSDictionary *result1, NSDictionary *result2);

BOOL areObjectsEqual(id obj1, id obj2);

id jsonObjectFromVariableObject(id escapedJson, NSDictionary *variableJson);

NSArray *valuesFromDataTable(NSDictionary *userInfo);

id convertParaFrom(NSDictionary *expectParaTable);
NSString *convertErrorMessageFrom(NSDictionary *expectParaTable);
BOOL isEqualsRoomList(NSArray *actualArray, NSArray *expectArray);
NSArray<NSArray<NSString *> *> *getListsFromDateTable(NSDictionary *expectAttrTable);
id<UDFamilyDelegate> createMockFamily(NSDictionary *familyDict);
id<UDFamilyMemberDelegate> createMockFamilyMember(NSDictionary *memberDict);
NSArray *getRoomListFromDateTable(NSDictionary *userInfo);
NSArray *getFloorInfoArrFromDateTable(NSDictionary *userInfo);
NSArray *getFloorInfoArrFromDateTable2(NSDictionary *userInfo);

BOOL isEqualsFamilyList(id obj1, id obj2);

NS_ASSUME_NONNULL_END
