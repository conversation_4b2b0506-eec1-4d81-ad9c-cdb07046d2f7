//
//  NSArray+Lite.m
//  UpStoragePluginTests
//
//  Created by 张虎 on 2021/6/2.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "NSArray+Lite.h"
#import "NSDictionary+SubMap.h"

@implementation NSArray (Lite)

- (BOOL)areElementsSubMapOfElementsOfArray:(NSArray<NSDictionary *> *)arr
{
    if (self.count != arr.count) {
        return NO;
    }

    BOOL __block result = YES;
    NSInteger count = arr.count;
    for (NSInteger i = 0; i < count; ++i) {
        NSDictionary *expected = self[i];
        NSDictionary *real = arr[i];
        result = [expected isSubDictionaryOfDict:real];
        if (!result) {
            break;
        }
    }
    return result;
}

@end
