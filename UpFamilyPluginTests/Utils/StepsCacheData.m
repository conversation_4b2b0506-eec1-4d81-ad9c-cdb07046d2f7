//
//  StepsCacheData.m
//  UpStoragePluginTests
//
//  Created by 张虎 on 2021/5/25.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepsCacheData.h"

@implementation StepsCacheData

- (instancetype)init
{
    self = [super init];
    if (self) {
        _actions = [NSMutableDictionary dictionary];
        _action2ExecuteResult = [NSMutableDictionary dictionary];
    }
    return self;
}

- (UpPluginAction *)action
{
    if (_action) {
        return _action;
    }
    else {
        return self.actions.allValues.lastObject;
    }
}

- (id)executeResult
{
    if (_executeResult) {
        return _executeResult;
    }
    else {
        return self.action2ExecuteResult.allValues.lastObject;
    }
}

@end
