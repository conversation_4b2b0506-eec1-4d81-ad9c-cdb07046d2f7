//
//  UpFamilyDeclaration.h
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/27.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - plugin param info
extern NSString *const kFamilyPluginFamilyId;
extern NSString *const kFamilyPluginFloorId;
extern NSString *const kFamilyPluginFloorOrderId;
extern NSString *const kFamilyPluginPhone;
extern NSString *const kFamilyPluginRoomName;
extern NSString *const kFamilyPluginFloorName;
extern NSString *const kFamilyPluginFloorLabel;
extern NSString *const kFamilyPluginFloorLogo;
extern NSString *const kFamilyPluginFloorPicture;
extern NSString *const kFamilyPluginRoomClass;
extern NSString *const kFamilyPluginRoomLabel;
extern NSString *const kFamilyPluginRoomLogo;
extern NSString *const kFamilyPluginRoomPicture;
extern NSString *const kFamilyPluginFamilyPosition;
extern NSString *const kFamilyPluginFamilyName;
extern NSString *const kFamilyPluginFamilyLocation;
extern NSString *const kFamilyPluginLongitude;
extern NSString *const kFamilyPluginLatitude;
extern NSString *const kFamilyPluginCityCode;
extern NSString *const kFamilyPluginRoomNames;
extern NSString *const kFamilyPluginRoomSortList;
extern NSString *const kFamilyPluginUserId;
extern NSString *const kFamilyPluginNickname;
extern NSString *const kFamilyPluginDeviceName;
extern NSString *const kFamilyPluginDeviceId;
extern NSString *const kFamilyPluginRoomId;
extern NSString *const kFamilyPluginNewRoomId;
extern NSString *const kFamilyPluginDeviceIds;
extern NSString *const kFamilyPluginVirtualUCId;
extern NSString *const kFamilyPluginUserFamilyName;
extern NSString *const kFamilyPluginMemberId;
extern NSString *const kFamilyPluginMemberName;
extern NSString *const kFamilyPluginAvatarUrl;
extern NSString *const kFamilyPluginIsCreater;
extern NSString *const kFamilyPluginNewFamilyId;
extern NSString *const kFamilyPluginBirthday;
extern NSString *const kFamilyPluginMemberRole;
extern NSString *const kFamilyPluginMemberType;
extern NSString *const kFamilyPluginInviteCode;
extern NSString *const kFamilyPluginAgree;
extern NSString *const kFamilyPluginCheckLevel;
extern NSString *const kFamilyPluginType;
extern NSString *const kFamilyPluginShareUuid;
extern NSString *const kFamilyPluginShareUuids;
#pragma mark - plugin error info
extern NSString *const kFamilyPluginRetCodeSuccess;
extern NSString *const kFamilyPluginRetInfoSuccess;

extern NSString *const kFamilyPluginRetCodeUserNotLogin;
extern NSString *const kFamilyPluginRetInfoUserNotLogin;

extern NSString *const kFamilyPluginRetCodeUserDataNotRefresh;
extern NSString *const kFamilyPluginRetInfoUserDataNotRefresh;

extern NSString *const kFamilyPluginCodeParamsError;
extern NSString *const kFamilyPluginInfoParamsError;

extern NSString *const kFamilyPluginRetCodeFloorNotFound;
extern NSString *const kFamilyPluginRetInfoFloorNotFound;

extern NSString *const kFamilyPluginRetCodeFamilyNotFound;
extern NSString *const kFamilyPluginRetInfoFamilyNotFound;

extern NSString *const kFamilyPluginRetCodeDeviceNotFound;
extern NSString *const kFamilyPluginRetInfoDeviceNotFound;

extern NSString *const kFamilyPluginRetCodeRoomNotFound;
extern NSString *const kFamilyPluginRetInfoRoomNotFound;

extern NSString *const kFamilyPluginRetCodeTargetFamilyNotFound;
extern NSString *const kFamilyPluginRetInfoTargetFamilyNotFound;

extern NSString *const kFamilyPluginRetCodeBatchDeviceNotFound;
extern NSString *const kFamilyPluginRetInfoBatchDeviceNotFound;
NS_ASSUME_NONNULL_END
