//
//  UpFamilyDeclaration.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/27.
//

#import "UpFamilyDeclaration.h"

#pragma mark - plugin param info
NSString *const kFamilyPluginFamilyId = @"familyId";
NSString *const kFamilyPluginFloorId = @"floorId";
NSString *const kFamilyPluginFloorOrderId = @"floorOrderId";
NSString *const kFamilyPluginPhone = @"phone";
NSString *const kFamilyPluginRoomName = @"roomName";
NSString *const kFamilyPluginFloorName = @"floorName";
NSString *const kFamilyPluginFloorLabel = @"floorLabel";
NSString *const kFamilyPluginFloorLogo = @"floorLogo";
NSString *const kFamilyPluginFloorPicture = @"floorPicture";
NSString *const kFamilyPluginRoomClass = @"roomClass";
NSString *const kFamilyPluginRoomLabel = @"roomLabel";
NSString *const kFamilyPluginRoomLogo = @"roomLogo";
NSString *const kFamilyPluginRoomPicture = @"roomPicture";

NSString *const kFamilyPluginFamilyPosition = @"familyPosition";
NSString *const kFamilyPluginFamilyName = @"familyName";
NSString *const kFamilyPluginFamilyLocation = @"familyLocation";
NSString *const kFamilyPluginLongitude = @"longitude";
NSString *const kFamilyPluginLatitude = @"latitude";
NSString *const kFamilyPluginCityCode = @"cityCode";
NSString *const kFamilyPluginRoomNames = @"roomNames";
NSString *const kFamilyPluginRoomSortList = @"sortList";
NSString *const kFamilyPluginUserId = @"userId";
NSString *const kFamilyPluginNickname = @"nickname";
NSString *const kFamilyPluginDeviceName = @"deviceName";
NSString *const kFamilyPluginDeviceId = @"deviceId";
NSString *const kFamilyPluginRoomId = @"roomId";
NSString *const kFamilyPluginNewRoomId = @"newRoomId";
NSString *const kFamilyPluginDeviceIds = @"deviceIds";
NSString *const kFamilyPluginVirtualUCId = @"virtualUCId";
NSString *const kFamilyPluginUserFamilyName = @"userFamilyName";
NSString *const kFamilyPluginMemberId = @"memberId";
NSString *const kFamilyPluginMemberName = @"memberName";
NSString *const kFamilyPluginAvatarUrl = @"avatarUrl";
NSString *const kFamilyPluginIsCreater = @"isCreater";
NSString *const kFamilyPluginNewFamilyId = @"newFamilyId";
NSString *const kFamilyPluginBirthday = @"birthday";
NSString *const kFamilyPluginMemberRole = @"memberRole";
NSString *const kFamilyPluginMemberType = @"memberType";
NSString *const kFamilyPluginInviteCode = @"inviteCode";
NSString *const kFamilyPluginAgree = @"agree";
NSString *const kFamilyPluginCheckLevel = @"checkLevel";
NSString *const kFamilyPluginType = @"type";
NSString *const kFamilyPluginShareUuid = @"shareUuid";
NSString *const kFamilyPluginShareUuids = @"shareUuids";
#pragma mark - plugin error info
NSString *const kFamilyPluginRetCodeSuccess = @"000000";
NSString *const kFamilyPluginRetInfoSuccess = @"操作成功";

NSString *const kFamilyPluginRetCodeUserNotLogin = @"110001";
NSString *const kFamilyPluginRetInfoUserNotLogin = @"用户未登录";

NSString *const kFamilyPluginRetCodeUserDataNotRefresh = @"110002";
NSString *const kFamilyPluginRetInfoUserDataNotRefresh = @"用户数据未刷新完成";

NSString *const kFamilyPluginCodeParamsError = @"900003";
NSString *const kFamilyPluginInfoParamsError = @"非法参数错误";

NSString *const kFamilyPluginRetCodeFloorNotFound = @"110008";
NSString *const kFamilyPluginRetInfoFloorNotFound = @"楼层不存在";

NSString *const kFamilyPluginRetCodeFamilyNotFound = @"110003";
NSString *const kFamilyPluginRetInfoFamilyNotFound = @"家庭不存在";

NSString *const kFamilyPluginRetCodeDeviceNotFound = @"110007";
NSString *const kFamilyPluginRetInfoDeviceNotFound = @"家庭设备不存在";

NSString *const kFamilyPluginRetCodeRoomNotFound = @"110004";
NSString *const kFamilyPluginRetInfoRoomNotFound = @"房间不存在";

NSString *const kFamilyPluginRetCodeTargetFamilyNotFound = @"110009";
NSString *const kFamilyPluginRetInfoTargetFamilyNotFound = @"目标家庭不存在";

NSString *const kFamilyPluginRetCodeBatchDeviceNotFound = @"110010";
NSString *const kFamilyPluginRetInfoBatchDeviceNotFound = @"批量操作设备列表为空";
