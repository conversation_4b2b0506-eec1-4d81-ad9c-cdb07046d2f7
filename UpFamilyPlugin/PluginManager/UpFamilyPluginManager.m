//
//  + (instancetype)sharedInstance;  UpFamilyPluginManager.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/27.
//

#import "UpFamilyPluginManager.h"
#import <UpPluginFoundation/UpPluginActionManager.h>
#import <UpPluginFoundation/UpPluginActionProtocol.h>
#import "UpGetFamiliesAction.h"
#import "UpGetCurrentFamilyAction.h"
#import "UpSetCurrentFamilyAction.h"
#import "UpEditFamilyFloorAction.h"
#import "UpAdminInviteMemberAction.h"
#import "UpAdminDeleteMemberAction.h"
#import "UpDeleteFamilyFloorAction.h"
#import "UpCreateFamilyAction.h"
#import "UpCreateFamilyFloorAction.h"
#import "UpFamilyAddRoomAction.h"
#import "UpUpdateFamilyInfoAction.h"
#import "UpChangeFamilyAdminAction.h"
#import "UpExitFamilyAsAdminAction.h"
#import "UpExitFamilyAsMemberAction.h"
#import "UpDeleteFamilyAsAdminAction.h"
#import "UpUpdateDeviceNameAction.h"
#import "UpRefreshRoomListAction.h"
#import "UpFamilyRemoveRoomAction.h"
#import "UpUpdateRoomNameAction.h"
#import "UpMoveDevicesToOtherRoomAction.h"
#import "UpAdminDeleteMemberAction.h"
#import "UpAddVirtualMemberAction.h"
#import "UpQueryFirstMemberAction.h"
#import "UpQueryFamilyInfoAction.h"
#import "UpModifyVirtualMemberAction.h"
#import "UpMoveDevicesToFamilyAction.h"
#import "UpUnbindDeviceFromFamilyAction.h"
#import "UpRemoveDeviceFromFamilyAction.h"
#import "UpModifyMemberRoleAction.h"
#import "UpModifyVirtualMemberRoleAction.h"
#import "UpSaveRoomsOrderAction.h"
#import "UpUpdateAndCheckDeviceNameAction.h"
#import "UpConfirmDeviceSharingRelationAction.h"
#import "UpCancelDeviceSharingRelationAction.h"
#import "UpModifyDeviceCardStatusAction.h"
#import "UpModifyDeviceAggregationAction.h"
#import "UpModifyAggregationSwitchAction.h"
#import "UpModifyMemberTypeAction.h"
#import "UpFamilyAddRoomNewAction.h"
#import "UpSaveRoomsOrderNewAction.h"

@implementation UpFamilyPluginManager
- (UpUserDomain *)userDomain
{
    return [UpUserDomainHolder instance].userDomain;
}

+ (UpFamilyPluginManager *)sharedInstance
{
    static UpFamilyPluginManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[UpFamilyPluginManager alloc] init];
    });
    return instance;
}

+ (void)load
{
    NSArray<Class<UpPluginActionProtocol>> *actions = @[

        UpGetFamiliesAction.class,
        UpGetCurrentFamilyAction.class,
        UpSetCurrentFamilyAction.class,
        UpEditFamilyFloorAction.class,
        UpCreateFamilyFloorAction.class,
        UpDeleteFamilyFloorAction.class,
        UpAdminDeleteMemberAction.class,
        UpCreateFamilyAction.class,
        UpFamilyAddRoomAction.class,
        UpUpdateFamilyInfoAction.class,
        UpChangeFamilyAdminAction.class,
        UpExitFamilyAsAdminAction.class,
        UpExitFamilyAsMemberAction.class,
        UpDeleteFamilyAsAdminAction.class,
        UpUpdateDeviceNameAction.class,
        UpRefreshRoomListAction.class,
        UpFamilyRemoveRoomAction.class,
        UpUpdateRoomNameAction.class,
        UpMoveDevicesToOtherRoomAction.class,
        UpAdminInviteMemberAction.class,
        UpAddVirtualMemberAction.class,
        UpQueryFirstMemberAction.class,
        UpQueryFamilyInfoAction.class,
        UpModifyVirtualMemberAction.class,
        UpMoveDevicesToFamilyAction.class,
        UpUnbindDeviceFromFamilyAction.class,
        UpRemoveDeviceFromFamilyAction.class,
        UpModifyMemberRoleAction.class,
        UpModifyVirtualMemberRoleAction.class,
        UpSaveRoomsOrderAction.class,
        UpUpdateAndCheckDeviceNameAction.class,
        UpConfirmDeviceSharingRelationAction.class,
        UpCancelDeviceSharingRelationAction.class,
        UpModifyDeviceCardStatusAction.class,
        UpModifyDeviceAggregationAction.class,
        UpModifyAggregationSwitchAction.class,
        UpModifyMemberTypeAction.class,
        UpFamilyAddRoomNewAction.class,
        UpSaveRoomsOrderNewAction.class
    ];
    UpPluginActionManager *manager = [UpPluginActionManager sharedInstance];
    [actions enumerateObjectsUsingBlock:^(Class<UpPluginActionProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [manager appendAction:[obj action] creator:obj];
    }];
}
@end
