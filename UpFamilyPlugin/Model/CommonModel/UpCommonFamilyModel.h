//
//  UpCommonFamilyModel.h
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/9/3.
//

#import <Foundation/Foundation.h>
#import <upuserdomain/UDMemberInfoDelegate.h>
#import <upuserdomain/UDRoomDelegate.h>
#import <upuserdomain/UDFloorInfoDelegate.h>
#import <upuserdomain/UDFamilyLocationDelegate.h>
#import <upuserdomain/UDFamilyInfoDelegate.h>

NS_ASSUME_NONNULL_BEGIN
///家庭定位模型
@interface UpFamilyLocationModel : NSObject
//家庭所在城市代码
@property (nonatomic, strong) NSString *cityCode;
//经度
@property (nonatomic, assign) double longitude;
//纬度
@property (nonatomic, assign) double latitude;

- (instancetype)initWithFamilyLocation:(id<UDFamilyLocationDelegate>)familyLocation;

@end

///家庭信息模型
@interface UpFamilyInfoModel : NSObject
//家庭名称
@property (nonatomic, strong) NSString *familyName;
//家庭定位信息
@property (nonatomic, strong) UpFamilyLocationModel *familyLocation;
//家庭位置
@property (nonatomic, strong) NSString *familyPosition;

- (instancetype)initWithFamilyInfo:(id<UDFamilyInfoDelegate>)familyInfo;
@end

///成员信息模型
@interface UpMemberInfoModel : NSObject
//用户id
@property (nonatomic, strong) NSString *userId;
//用户名
@property (nonatomic, strong) NSString *name;
//手机号
@property (nonatomic, strong) NSString *mobile;
//头像url
@property (nonatomic, strong) NSString *avatarUrl;
//是否虚拟用户
@property (nonatomic, assign) BOOL virtualUserFlag;
//用户中心UserId
@property (nonatomic, strong) NSString *ucUserId;
//宿主用户的IOT平台userId
@property (nonatomic, strong) NSString *hostUserId;
//用户生日
@property (nonatomic, copy) NSString *birthday;

- (instancetype)initWithMemberInfo:(id<UDMemberInfoDelegate>)memberInfo;

@end

///房间模型
@interface UpRoomModel : NSObject
//房间id
@property (nonatomic, strong) NSString *roomId;
//房间名称
@property (nonatomic, strong) NSString *roomName;
//房间所属的家庭id
@property (nonatomic, strong) NSString *familyId;
//房间类型
@property (nonatomic, strong) NSString *roomClass;
//房间标签
@property (nonatomic, strong) NSString *roomLabel;
//房间logo url
@property (nonatomic, strong) NSString *roomLogo;
//房间图片 url
@property (nonatomic, strong) NSString *roomPicture;
/// 房间排序码
@property (nonatomic, strong) NSString *sortCode;

- (instancetype)initWithRoomDelegate:(id<UDRoomDelegate>)room familyId:(NSString *)familyId;

- (instancetype)initWithRoomDelegateForDeviceInfo:(id<UDRoomDelegate>)room familyId:(NSString *)familyId;

@end


///楼层模型
@interface UpFloorInfoModel : NSObject
//楼层id
@property (nonatomic, strong) NSString *floorId;
//楼层次序
@property (nonatomic, strong) NSString *floorOrderId;
//楼层名称
@property (nonatomic, strong) NSString *floorName;
//楼层标签
@property (nonatomic, strong) NSString *floorLabel;
//楼层类型
@property (nonatomic, strong) NSString *floorClass;
//楼层logo url
@property (nonatomic, strong) NSString *floorLogo;
//楼层图片url
@property (nonatomic, strong) NSString *floorPicture;
//楼层创建时间
@property (nonatomic, strong) NSString *floorCreateTime;
//楼层房间列表
@property (nonatomic, strong) NSArray<UpRoomModel *> *rooms;

- (instancetype)initWithUDFloorInfo:(id<UDFloorInfoDelegate>)floorInfo familyId:(NSString *)familyId;
@end

@interface UpCommonFamilyModel : NSObject

@end

NS_ASSUME_NONNULL_END
