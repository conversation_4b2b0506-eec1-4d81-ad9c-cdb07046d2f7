//
//  UpCommonFamilyModel.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/9/3.
//

#import "UpCommonFamilyModel.h"
@implementation UpFamilyLocationModel
- (instancetype)initWithFamilyLocation:(id<UDFamilyLocationDelegate>)familyLocation
{
    if (self = [super init]) {
        self.cityCode = familyLocation.cityCode ?: @"";
        self.longitude = [familyLocation.longitude doubleValue] ?: 0.0;
        self.latitude = [familyLocation.latitude doubleValue] ?: 0.0;
    }

    return self;
}
@end

@implementation UpFamilyInfoModel
- (instancetype)initWithFamilyInfo:(id<UDFamilyInfoDelegate>)familyInfo
{
    if (self = [super init]) {
        self.familyName = familyInfo.familyName ?: @"";
        self.familyLocation = [[UpFamilyLocationModel alloc] initWithFamilyLocation:familyInfo.familyLocation];
        self.familyPosition = familyInfo.familyPosition ?: @"";
    }

    return self;
}
@end


@implementation UpMemberInfoModel
- (instancetype)initWithMemberInfo:(id<UDMemberInfoDelegate>)memberInfo
{
    if (self = [super init]) {
        self.userId = memberInfo.userId ?: @"";
        self.name = memberInfo.name ?: @"";
        self.mobile = memberInfo.mobile ?: @"";
        self.avatarUrl = memberInfo.avatarUrl ?: @"";
        self.virtualUserFlag = memberInfo.virtualUserFlag;
        self.ucUserId = memberInfo.ucUserId ?: @"";
        self.hostUserId = memberInfo.hostUserId ?: @"";
        self.birthday = memberInfo.birthday ?: @"";
    }
    return self;
}
@end

@implementation UpRoomModel

- (instancetype)initWithRoomDelegate:(id<UDRoomDelegate>)room familyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.roomId = room.roomId ?: @"";
        self.roomName = room.roomName ?: @"";
        self.familyId = familyId ?: @"";
        self.roomClass = room.roomClass ?: @"";
        self.roomLabel = room.roomLabel ?: @"";
        self.roomLogo = room.roomLogo ?: @"";
        self.roomPicture = room.roomPicture ?: @"";
        self.sortCode = room.sortCode ?: @"";
    }

    return self;
}

- (instancetype)initWithRoomDelegateForDeviceInfo:(id<UDRoomDelegate>)room familyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.roomId = room.roomId ?: @"";
        self.roomName = room.roomName ?: @"";
        self.familyId = familyId ?: @"";
    }

    return self;
}

@end


@implementation UpFloorInfoModel

- (instancetype)initWithUDFloorInfo:(id<UDFloorInfoDelegate>)floorInfo familyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.floorId = floorInfo.floorId ?: @"";
        self.floorOrderId = floorInfo.floorOrderId ?: @"";
        self.floorName = floorInfo.floorName ?: @"";
        self.floorLabel = floorInfo.floorLabel ?: @"";
        self.floorClass = floorInfo.floorClass ?: @"";
        self.floorLogo = floorInfo.floorLogo ?: @"";
        self.floorPicture = floorInfo.floorPicture ?: @"";
        self.floorCreateTime = floorInfo.floorCreateTime ?: @"";
        NSMutableArray<UpRoomModel *> *roomModels = [NSMutableArray array];
        for (id<UDRoomDelegate> room in floorInfo.rooms) {
            [roomModels addObject:[[UpRoomModel alloc] initWithRoomDelegate:room familyId:familyId]];
        }
        self.rooms = roomModels;
    }

    return self;
}
@end

@implementation UpCommonFamilyModel

@end
