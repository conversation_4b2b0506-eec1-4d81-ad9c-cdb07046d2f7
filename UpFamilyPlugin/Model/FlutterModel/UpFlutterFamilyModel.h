//
//  UpFlutterFamilyModel.h
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import <Foundation/Foundation.h>
#import <upuserdomain/UDFamilyMemberDelegate.h>
#import <upuserdomain/UDFamilyDelegate.h>
#import "UpCommonFamilyModel.h"

NS_ASSUME_NONNULL_BEGIN

///家庭管理员模型
@interface UpFlutterFamilyOwerModel : NSObject
//用户id
@property (nonatomic, strong) NSString *userId;
//用户名
@property (nonatomic, strong) NSString *name;
//手机号
@property (nonatomic, strong) NSString *mobile;
//头像url
@property (nonatomic, strong) NSString *avatarUrl;
//是否虚拟用户
@property (nonatomic, assign) BOOL virtualUserFlag;

- (instancetype)initWithMemberInfo:(id<UDMemberInfoDelegate>)memberInfo;
@end

///成员模型
@interface UpFlutterMemberModel : NSObject
//成员名称
@property (nonatomic, strong) NSString *memberName;
//成员信息
@property (nonatomic, strong) UpMemberInfoModel *memberInfo;
//加入时间
@property (nonatomic, strong) NSString *joinTime;
//成员身份
@property (nonatomic, copy) NSString *memberRole;
/// 角色类型: 0-创建者,1-管理员,2-成员
@property (nonatomic, assign) NSInteger memberType;

- (instancetype)initWithFamilyMember:(id<UDFamilyMemberDelegate>)familyMember;
@end

///家庭模型
@interface UpFlutterFamilyModel : NSObject
//家庭id
@property (nonatomic, strong) NSString *familyId;
//家庭创建时间
@property (nonatomic, strong) NSString *createTime;
//家庭位置修改标识位
@property (nonatomic, assign) BOOL locationChangeFlag;
//家庭信息
@property (nonatomic, strong) UpFamilyInfoModel *info;
//管理员信息
@property (nonatomic, strong) UpFlutterFamilyOwerModel *owner;
//管理员信息Id
@property (nonatomic, strong) NSString *ownerId;
//第一个加入家庭的成员
@property (nonatomic, strong) UpFlutterMemberModel *firstMember;
//家庭成员列表
@property (nonatomic, strong) NSArray<UpFlutterMemberModel *> *members;
//家庭楼层列表
@property (nonatomic, strong) NSArray<UpFloorInfoModel *> *floorInfos;

/// 当前用户在该家庭中的角色类型,0-创建者,1-管理员,2-普通成员
@property (nonatomic) NSInteger memberType;

/// 当前用户加入该家庭的时间
@property (nonatomic, copy) NSString *joinTime;

- (instancetype)initWithFamilyModel:(id<UDFamilyDelegate>)family;

@end

NS_ASSUME_NONNULL_END
