//
//  UpFlutterFamilyModel.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpFlutterFamilyModel.h"
@implementation UpFlutterFamilyOwerModel
- (instancetype)initWithMemberInfo:(id<UDMemberInfoDelegate>)memberInfo
{
    if (self = [super init]) {
        self.userId = memberInfo.userId ?: @"";
        self.name = memberInfo.name ?: @"";
        self.mobile = memberInfo.mobile ?: @"";
        self.avatarUrl = memberInfo.avatarUrl ?: @"";
        self.virtualUserFlag = memberInfo.virtualUserFlag;
    }
    return self;
}
@end


@implementation UpFlutterMemberModel
- (instancetype)initWithFamilyMember:(id<UDFamilyMemberDelegate>)familyMember
{
    if (self = [super init]) {
        self.memberName = familyMember.memberName ?: @"";
        self.memberInfo = [[UpMemberInfoModel alloc] initWithMemberInfo:familyMember.memberInfo];
        self.joinTime = familyMember.joinTime ?: @"";
        self.memberRole = familyMember.memberRole ?: @"";
        self.memberType = familyMember.memberType;
    }

    return self;
}
@end


@implementation UpFlutterFamilyModel
- (instancetype)initWithFamilyModel:(id<UDFamilyDelegate>)family
{
    if (self = [super init]) {
        self.familyId = family.familyId ?: @"";
        self.createTime = family.createTime ?: @"";
        self.locationChangeFlag = family.locationChangeFlag;
        self.info = [[UpFamilyInfoModel alloc] initWithFamilyInfo:family.info];
        self.owner = [[UpFlutterFamilyOwerModel alloc] initWithMemberInfo:family.owner];
        self.ownerId = family.ownerId ?: @"";
        self.firstMember = [[UpFlutterMemberModel alloc] initWithFamilyMember:family.firstMember];
        NSMutableArray<UpFlutterMemberModel *> *memberModels = [NSMutableArray array];
        for (id<UDFamilyMemberDelegate> familyMember in family.members) {
            UpFlutterMemberModel *member = [[UpFlutterMemberModel alloc] initWithFamilyMember:familyMember];
            [memberModels addObject:member];
        }
        self.members = memberModels;
        NSMutableArray<UpFloorInfoModel *> *floorModels = [NSMutableArray array];
        for (id<UDFloorInfoDelegate> foolrInfo in family.floorInfos) {
            UpFloorInfoModel *floorModel = [[UpFloorInfoModel alloc] initWithUDFloorInfo:foolrInfo familyId:family.familyId];
            [floorModels addObject:floorModel];
        }
        self.floorInfos = floorModels;
        self.memberType = family.memberType;
        self.joinTime = family.joinTime;
    }

    return self;
}
@end
