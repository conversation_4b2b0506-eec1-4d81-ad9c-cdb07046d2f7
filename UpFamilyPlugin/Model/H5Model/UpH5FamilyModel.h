//
//  UpH5FamilyModel.h
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import <Foundation/Foundation.h>
#import <upuserdomain/UDDeviceDelegate.h>
#import <upuserdomain/UDDeviceOwnerInfoDelegate.h>
#import <upuserdomain/UDDeviceAuthDelegate.h>
#import <upuserdomain/UDDevicePermissionDelegate.h>
#import "UpCommonFamilyModel.h"
#import <upuserdomain/UDFamilyMemberDelegate.h>
#import <upuserdomain/UDFamilyDelegate.h>

NS_ASSUME_NONNULL_BEGIN

///拥有者信息
@interface UpH5OwnerDetailModel : NSObject
//用户ID
@property (nonatomic, copy) NSString *userId;
//手机号
@property (nonatomic, copy) NSString *mobile;

- (instancetype)initWithDeviceOwnerInfo:(id<UDDeviceOwnerInfoDelegate>)ownerInfo;

@end

@interface UpH5AuthDetailModel : NSObject
//控制
@property (nonatomic, assign) BOOL control;
//设置
@property (nonatomic, assign) BOOL set;
//查看
@property (nonatomic, assign) BOOL view;

- (instancetype)initWithDeviceAuth:(id<UDDeviceAuthDelegate>)auth;
@end

///权限信息模型
@interface UpH5PermissionDetailModel : NSObject
//权限信息
@property (nonatomic, strong) UpH5AuthDetailModel *auth;
//权限类型
@property (nonatomic, copy) NSString *authType;

- (instancetype)initWithDevicePermission:(id<UDDevicePermissionDelegate>)permission;

@end

///设备
@interface UpH5DeviceInfoModel : NSObject
// 应用分类
@property (nonatomic, copy) NSString *apptypeName;
// 品牌
@property (nonatomic, copy) NSString *brand;
//设备ID
@property (nonatomic, copy) NSString *deviceId;
//设备名称
@property (nonatomic, copy) NSString *deviceName;
//设备类型
@property (nonatomic, copy) NSString *deviceType;
//
@property (nonatomic, copy) NSString *deviceRole;
//
@property (nonatomic, copy) NSString *deviceRoleType;
//实物图1
@property (nonatomic, copy) NSString *imageAddr1;
//实物图2
@property (nonatomic, copy) NSString *imageAddr2;
//是否在线
@property (nonatomic, assign) BOOL isOnline;
//型号
@property (nonatomic, copy) NSString *model;
//
@property (nonatomic, copy) NSString *wifiType;
//产品编码
@property (nonatomic, copy) NSString *prodNo;
//
@property (nonatomic, copy) NSString *bindType;
//
@property (nonatomic, copy) NSString *bindTime;
//
@property (nonatomic, copy) NSString *comunicationMode;
//是否归当前用户所有
@property (nonatomic, copy) NSString *ownerId;
//拥有者信息
@property (nonatomic, strong) UpH5OwnerDetailModel *ownerInfo;
//权限信息
@property (nonatomic, strong) UpH5PermissionDetailModel *permission;
//房间信息
@property (nonatomic, strong) UpRoomModel *room;
@property (nonatomic, copy) NSString *devFloorId;
@property (nonatomic, copy) NSString *devFloorOrderId;
@property (nonatomic, copy) NSString *devFloorName;
@property (nonatomic, copy) NSString *deviceNetType;
// 0:保活  1：非保活
@property (nonatomic, assign) NSInteger noKeepAlive;
@property (nonatomic, assign) BOOL supportShared;
@property (nonatomic, assign) BOOL sharedDeviceFlag;

/// 是否支持二次绑定
@property (nonatomic, assign) BOOL reBind;

- (instancetype)initWithDeviceInfo:(id<UDDeviceDelegate>)device;
@end

///成员模型
@interface UpH5MemberModel : NSObject
//成员名称
@property (nonatomic, strong) NSString *memberName;
//成员信息
@property (nonatomic, strong) UpMemberInfoModel *memberInfo;
//加入时间
@property (nonatomic, strong) NSString *joinTime;
//分享设备个数
@property (nonatomic, assign) NSInteger shareDeviceCount;
//家庭Id
@property (nonatomic, strong) NSString *familyId;
//成员身份
@property (nonatomic, copy) NSString *memberRole;
/// 角色类型: 0-创建者,1-管理员,2-成员
@property (nonatomic, assign) NSInteger memberType;

- (instancetype)initWithFamilyMember:(nullable id<UDFamilyMemberDelegate>)familyMember;
@end


///家庭管理员模型
@interface UpH5FamilyOwerModel : NSObject
//用户id
@property (nonatomic, strong) NSString *userId;
//用户名
@property (nonatomic, strong) NSString *name;
//手机号
@property (nonatomic, strong) NSString *mobile;
//头像url
@property (nonatomic, strong) NSString *avatarUrl;
//用户中心UserId
@property (nonatomic, strong) NSString *ucUserId;

- (instancetype)initWithMemberInfo:(id<UDMemberInfoDelegate>)memberInfo;
@end


///H5家庭模型
@interface UpH5FamilyModel : NSObject
//家庭id
@property (nonatomic, strong) NSString *familyId;
//家庭创建时间
@property (nonatomic, strong) NSString *createTime;
//家庭位置修改标识位
@property (nonatomic, assign) BOOL locationChangeFlag;
//家庭信息
@property (nonatomic, strong) UpFamilyInfoModel *info;
//管理员信息
@property (nonatomic, strong) UpH5FamilyOwerModel *owner;
//管理员信息Id
@property (nonatomic, strong) NSString *familyOwner;
//第一个加入家庭的成员
@property (nonatomic, strong) UpH5MemberModel *firstMember;
//家庭成员列表
@property (nonatomic, strong) NSArray<UpH5MemberModel *> *members;
//家庭楼层列表
@property (nonatomic, strong) NSArray<UpFloorInfoModel *> *floorInfos;
//设备
@property (nonatomic, strong) NSArray<UpH5DeviceInfoModel *> *sharedDevices;

/// 当前用户在该家庭中的角色类型,0-创建者,1-管理员,2-普通成员
@property (nonatomic) NSInteger memberType;

/// 当前用户加入该家庭的时间
@property (nonatomic, copy) NSString *joinTime;

- (instancetype)initWithFamilyModel:(nullable id<UDFamilyDelegate>)family;
@end

NS_ASSUME_NONNULL_END
