//
//  UpH5FamilyModel.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpH5FamilyModel.h"

@implementation UpH5OwnerDetailModel
- (instancetype)initWithDeviceOwnerInfo:(id<UDDeviceOwnerInfoDelegate>)ownerInfo
{
    if (self = [super init]) {
        self.userId = ownerInfo.userId ?: @"";
        self.mobile = ownerInfo.mobile ?: @"";
    }

    return self;
}

@end
@implementation UpH5AuthDetailModel
- (instancetype)initWithDeviceAuth:(id<UDDeviceAuthDelegate>)auth
{
    if (self = [super init]) {
        self.control = auth.control;
        self.set = auth.set;
        self.view = auth.view;
    }

    return self;
}

@end

@implementation UpH5PermissionDetailModel
- (instancetype)initWithDevicePermission:(id<UDDevicePermissionDelegate>)permission
{
    if (self = [super init]) {
        self.auth = [[UpH5AuthDetailModel alloc] initWithDeviceAuth:permission.auth];
        self.authType = permission.authType ?: @"";
    }

    return self;
}

@end

@implementation UpH5DeviceInfoModel
- (instancetype)initWithDeviceInfo:(id<UDDeviceDelegate>)device
{
    if (self = [super init]) {
        self.apptypeName = device.apptypeName ?: @"";
        self.brand = device.brand ?: @"";
        self.deviceId = device.deviceId ?: @"";
        self.deviceName = device.deviceName ?: @"";
        self.deviceType = device.deviceType ?: @"";
        self.deviceRole = device.deviceRole ?: @"";
        self.deviceRoleType = device.deviceRoleType ?: @"";
        self.imageAddr1 = device.imageAddr1 ?: @"";
        self.imageAddr2 = device.imageAddr2 ?: @"";
        self.isOnline = device.isOnline;
        self.model = device.model ?: @"";
        self.wifiType = device.wifiType ?: @"";
        self.prodNo = device.prodNo ?: @"";
        self.bindTime = device.bindTime ?: @"";
        self.bindType = device.bindType ?: @"";
        self.comunicationMode = device.comunicationMode ?: @"";
        self.ownerId = device.ownerId ?: @"";
        self.devFloorId = device.devFloorId ?: @"";
        self.devFloorName = device.devFloorName ?: @"";
        self.devFloorOrderId = device.devFloorOrderId ?: @"";
        self.deviceNetType = device.deviceNetType ?: @"";
        self.noKeepAlive = device.noKeepAlive;
        self.ownerInfo = [[UpH5OwnerDetailModel alloc] initWithDeviceOwnerInfo:device.ownerInfo];
        self.permission = [[UpH5PermissionDetailModel alloc] initWithDevicePermission:device.permission];
        self.room = [[UpRoomModel alloc] initWithRoomDelegateForDeviceInfo:device.room familyId:device.familyId];
        self.supportShared = device.supportShared;
        self.sharedDeviceFlag = device.sharedDeviceFlag;
        self.reBind = device.reBind;
    }

    return self;
}

@end


@implementation UpH5MemberModel
- (instancetype)initWithFamilyMember:(nullable id<UDFamilyMemberDelegate>)familyMember
{
    if (self = [super init]) {
        self.memberName = familyMember.memberName ?: @"";
        self.memberInfo = [[UpMemberInfoModel alloc] initWithMemberInfo:familyMember.memberInfo];
        self.joinTime = familyMember.joinTime ?: @"";
        self.shareDeviceCount = familyMember.shareDeviceCount;
        self.familyId = familyMember.familyId ?: @"";
        self.memberRole = familyMember.memberRole ?: @"";
        self.memberType = familyMember.memberType;
    }

    return self;
}
@end

@implementation UpH5FamilyOwerModel
- (instancetype)initWithMemberInfo:(id<UDMemberInfoDelegate>)memberInfo
{
    if (self = [super init]) {
        self.userId = memberInfo.userId ?: @"";
        self.name = memberInfo.name ?: @"";
        self.mobile = memberInfo.mobile ?: @"";
        self.avatarUrl = memberInfo.avatarUrl ?: @"";
        self.ucUserId = memberInfo.ucUserId ?: @"";
    }
    return self;
}
@end


@implementation UpH5FamilyModel
- (instancetype)initWithFamilyModel:(nullable id<UDFamilyDelegate>)family
{
    if (self = [super init]) {
        self.familyId = family.familyId ?: @"";
        self.createTime = family.createTime ?: @"";
        self.locationChangeFlag = family.locationChangeFlag;
        self.info = [[UpFamilyInfoModel alloc] initWithFamilyInfo:family.info];
        self.owner = [[UpH5FamilyOwerModel alloc] initWithMemberInfo:family.owner];
        self.familyOwner = family.ownerId;
        self.firstMember = [[UpH5MemberModel alloc] initWithFamilyMember:family.firstMember];
        NSMutableArray<UpH5MemberModel *> *memberModels = [NSMutableArray array];
        for (id<UDFamilyMemberDelegate> familyMember in family.members) {
            UpH5MemberModel *member = [[UpH5MemberModel alloc] initWithFamilyMember:familyMember];
            [memberModels addObject:member];
        }
        self.members = memberModels;
        NSMutableArray<UpFloorInfoModel *> *floorModels = [NSMutableArray array];
        for (id<UDFloorInfoDelegate> foolrInfo in family.floorInfos) {
            UpFloorInfoModel *floorModel = [[UpFloorInfoModel alloc] initWithUDFloorInfo:foolrInfo familyId:family.familyId];
            [floorModels addObject:floorModel];
        }
        self.floorInfos = floorModels;
        NSMutableArray<UpH5DeviceInfoModel *> *deviceModels = [NSMutableArray array];
        for (id<UDDeviceDelegate> device in family.getDeviceList) {
            UpH5DeviceInfoModel *deviceModel = [[UpH5DeviceInfoModel alloc] initWithDeviceInfo:device];
            [deviceModels addObject:deviceModel];
        }
        self.sharedDevices = deviceModels;
        self.memberType = family.memberType;
        self.joinTime = family.joinTime;
    }

    return self;
}
@end
