//
//  BatchProcessDevice+Tools.m
//  UpFamilyPlugin
//
//  Created by 吴子航 on 2022/2/8.
//

#import "BatchProcessDevice+UFPTools.h"

@implementation BatchProcessDeviceResult (UFPTools)

- (NSDictionary *)transformToPluginResult
{
    NSArray * (^transformBlock)(NSArray<BatchProcessDevice *> *) = ^(NSArray<BatchProcessDevice *> *dataSource) {
      NSMutableArray *deviceList = [NSMutableArray array];
      [dataSource enumerateObjectsUsingBlock:^(BatchProcessDevice *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        [deviceList addObject:@{
            @"deviceId" : obj.deviceId ?: @"",
            @"deviceName" : obj.deviceName ?: @""
        }];
      }];

      return [deviceList copy];
    };

    return @{
        @"successDevices" : transformBlock(self.successDevices),
        @"failureDevices" : transformBlock(self.failureDevices)
    };
}

@end
