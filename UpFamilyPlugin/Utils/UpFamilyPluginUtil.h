//
//  UpFamilyPluginUtil.h
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/30.
//

#import <Foundation/Foundation.h>
#import <UPPluginBaseAPI/UPPCallBackProtocol.h>

NS_ASSUME_NONNULL_BEGIN

#define TypeCheck(cls, v, r) [UpFamilyPluginUtil typeCheck:cls value:v replace:r]
#define StringTypeCheck(v) TypeCheck(NSString.class, v, @"")

@interface UpFamilyPluginUtil : NSObject
+ (void)callBackResultParamsVerify:(id<UPPCallBackProtocol>)callBack params:(NSDictionary *)params;

+ (id)typeCheck:(Class)cls value:(id)value replace:(nullable id)replace;


/**
 检查action输入参数防止参数为NSNull类型
 
 @param paramter 参数值
 @param defaultValue 默认值
 @return return value description
 */
+ (id)checkNullParameter:(id)paramter defaultValue:(_Nullable id)defaultValue;

//是否为空
+ (BOOL)isNullOrEmpty:(NSString *)str;

@end

NS_ASSUME_NONNULL_END
