//
//  UpFamilyPluginUtil.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/30.
//

#import "UpFamilyPluginUtil.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyDeclaration.h"

@implementation UpFamilyPluginUtil

+ (void)callBackResultParamsVerify:(id<UPPCallBackProtocol>)callBack params:(NSDictionary *)params
{
    UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginCodeParamsError retInfo:[NSString stringWithFormat:@"非法参数错误(%@)", params.mj_JSONString]];
    [callBack onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
}

+ (id)typeCheck:(Class)cls value:(id)value replace:(id)replace
{
    if ([value isKindOfClass:cls]) {
        return value;
    }

    return [replace isKindOfClass:cls] ? replace : [cls new];
}

+ (id)checkNullParameter:(id)paramter defaultValue:(id)defaultValue
{
    if ([paramter isKindOfClass:NSNull.class]) {
        return defaultValue;
    }

    return paramter;
}

//是否为空
+ (BOOL)isNullOrEmpty:(NSString *)str
{
    return ([str isEqualToString:@""] || str == nil);
}

@end
