//
//  UpUpdateDeviceNameAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpUpdateDeviceNameAction.h"
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <MJExtension/MJExtension.h>
#import "UpH5FamilyModel.h"
#import <UPLog/UPLog.h>

static NSString *const UpdateDeviceName_ActionName = @"updateDeviceNameForFamily";
@implementation UpUpdateDeviceNameAction
+ (NSString *)action
{
    return UpdateDeviceName_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *deviceId = params[kFamilyPluginDeviceId];
    NSString *deviceName = params[kFamilyPluginDeviceName];
    if (!UPCommonFunc_isValidString(deviceId) || !UPCommonFunc_isValidString(deviceName)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    id<UDDeviceDelegate> device = [[UpFamilyPluginManager sharedInstance].userDomain.user getDeviceById:deviceId];
    if (device == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeDeviceNotFound retInfo:kFamilyPluginRetInfoDeviceNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    [device updateDeviceName:deviceName
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          NSString *deviceId = StringTypeCheck(result.retData);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:@{ @"deviceId" : deviceId }];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpUpdateDeviceNameAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
