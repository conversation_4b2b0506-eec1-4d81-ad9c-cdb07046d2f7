//
//  UpModifyVirtualMemberAction.m
//  UpFamilyPlugin
//
//  Created by 吴子航 on 2022/1/18.
//

#import "UpModifyVirtualMemberAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"

static NSString *const ModifyVirtualMember_ActionName = @"modifyVirtualMemberForFamily";

@implementation UpModifyVirtualMemberAction

+ (NSString *)action
{
    return ModifyVirtualMember_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *memberId = params[kFamilyPluginMemberId];
    NSString *memberName = params[kFamilyPluginMemberName];
    NSString *avatarUrl = StringTypeCheck(params[kFamilyPluginAvatarUrl]);
    BOOL isCreater = [params[kFamilyPluginIsCreater] boolValue];
    NSString *birthday = params[kFamilyPluginBirthday];

    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(memberId) || !UPCommonFunc_isValidString(memberName)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    VirtualMemberArgs *virtualMember = [[VirtualMemberArgs alloc] init];
    virtualMember.memberId = memberId;
    virtualMember.memberName = memberName;
    virtualMember.avatarUrl = avatarUrl;
    virtualMember.isCreater = isCreater;
    virtualMember.birthday = birthday;

    [family modifyVirtualMember:virtualMember
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpModifyVirtualMemberAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
