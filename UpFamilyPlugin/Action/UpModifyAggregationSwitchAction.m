//
//  UpModifyAggregationSwitchAction.m
//  UpFamilyPlugin
//
//  Created by l<PERSON><PERSON> on 2025/3/27.
//

#import "UpModifyAggregationSwitchAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UPCommonResult.h>
#import <uplog/UPLog.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <upuserdomain/AggregationSwitchArgs.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"

static NSString *const ModifyAggregationSwitch_ActionName = @"operateAggregationSwitchForFamily";

@implementation UpModifyAggregationSwitchAction

+ (NSString *)action
{
    return ModifyAggregationSwitch_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *source = params[@"source"];
    if (source.length < 1) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    NSArray<NSDictionary *> *familyAgg = params[@"familyAgg"];
    if (!familyAgg) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    NSMutableArray<FamilyAgg *> *familyAggs = [NSMutableArray array];
    for (NSDictionary *switchInfo in familyAgg) {
        FamilyAgg *aggInfo = [FamilyAgg mj_objectWithKeyValues:switchInfo];
        [familyAggs addObject:aggInfo];
    }
    AggregationSwitchArgs *args = [[AggregationSwitchArgs alloc] init];
    args.source = source;
    args.familyAgg = familyAggs;

    [UpUserDomainHolder.instance.userDomain.user modifyAggregationSwitch:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"UpFamilyPlugin", @"operateAggregationSwitchForFamily succecc:%@", result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"operateAggregationSwitchForFamily failure:%@", result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
