//
//  UpFamilyAddRoomNewAction.m
//  UpFamilyPlugin
//
//  Created by lubiao on 2025/4/11.
//

#import "UpFamilyAddRoomNewAction.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>
#import <UPUserDomain/RoomNewArgs.h>

static NSString *const FamilyAddRoomNew_ActionName = @"familyAddRoomNewForFamily";

@implementation UpFamilyAddRoomNewAction

+ (NSString *)action
{
    return FamilyAddRoomNew_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *roomName = params[kFamilyPluginRoomName];
    NSString *familyId = params[kFamilyPluginFamilyId];
    if (!UPCommonFunc_isValidString(roomName) || !UPCommonFunc_isValidString(familyId)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    RoomNewArgs *args = [[RoomNewArgs alloc] init];
    args.roomName = roomName;
    args.floorOrderId = params[kFamilyPluginFloorOrderId];
    args.roomClass = params[kFamilyPluginRoomClass];

    [family addRoomNew:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"UpFamilyPlugin", @"familyAddRoomNewForFamily success:%@", result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"familyAddRoomNewForFamily failure:%@", result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
