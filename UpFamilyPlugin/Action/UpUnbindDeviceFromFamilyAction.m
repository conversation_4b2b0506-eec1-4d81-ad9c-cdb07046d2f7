//
//  UpUnbindDeviceFromFamilyAction.m
//  UpFamilyPlugin
//
//  Created by 吴子航 on 2022/2/8.
//

#import "UpUnbindDeviceFromFamilyAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"
#import "BatchProcessDevice+UFPTools.h"

static NSString *const UnbindDeviceFromFamily_ActionName = @"unbindDeviceFromFamilyForFamily";

@implementation UpUnbindDeviceFromFamilyAction
+ (NSString *)action
{
    return UnbindDeviceFromFamily_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSArray *devices = TypeCheck(NSArray.class, params[kFamilyPluginDeviceIds], @[]);
    if (!UPCommonFunc_isValidString(familyId) || devices.count == 0) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    NSMutableArray *deviceArray = [NSMutableArray array];
    for (NSString *deviceId in devices) {
        id<UDDeviceDelegate> device = [[UpFamilyPluginManager sharedInstance].userDomain.user getDeviceById:deviceId];
        if (device) {
            [deviceArray addObject:device];
        }
    }

    if (deviceArray.count == 0) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeBatchDeviceNotFound retInfo:kFamilyPluginRetInfoBatchDeviceNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    [family unBindDevices:deviceArray
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          id retData = [NSNull null];
          if ([result.retData isKindOfClass:BatchProcessDeviceResult.class]) {
              retData = [(BatchProcessDeviceResult *)result.retData transformToPluginResult];
          }
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpUnbindDeviceFromFamilyAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
