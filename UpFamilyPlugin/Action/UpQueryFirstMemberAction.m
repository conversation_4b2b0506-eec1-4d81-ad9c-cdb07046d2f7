//
//  UpQueryFirstMemberAction.m
//  UpFamilyPlugin
//
//  Created by 吴子航 on 2022/1/18.
//

#import "UpQueryFirstMemberAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"
#import "UpH5FamilyModel.h"

static NSString *const QueryFirstMember_ActionName = @"queryFirstMemberForFamily";

@implementation UpQueryFirstMemberAction

+ (NSString *)action
{
    return QueryFirstMember_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];

    if (!UPCommonFunc_isValidString(familyId)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    [family queryFirstMemeberSuccess:^(UserDomainSampleResult *_Nonnull result) {
      id<UDFamilyMemberDelegate> member = result.retData;
      if (![member conformsToProtocol:@protocol(UDFamilyMemberDelegate)]) {
          member = nil;
      }
      UpH5MemberModel *memberModel = [[UpH5MemberModel alloc] initWithFamilyMember:member];
      UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:[memberModel mj_keyValues]];
      [callback onSuccess:[commonResult toJsonObject]];
      UPLogDebug(@"UpFamilyPlugin", @"%s[%d]UpQueryFirstMemberAction retData:%@", __PRETTY_FUNCTION__, __LINE__, commonResult.toJsonObject);
    }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpQueryFirstMemberAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
