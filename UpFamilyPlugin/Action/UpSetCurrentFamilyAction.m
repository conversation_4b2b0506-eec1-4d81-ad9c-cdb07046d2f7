//
//  UpSetCurrentFamilyAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpSetCurrentFamilyAction.h"
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>

static NSString *const SetCurrentFamily_ActionName = @"setCurrentFamilyForFamily";
@implementation UpSetCurrentFamilyAction
+ (NSString *)action
{
    return SetCurrentFamily_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    if (!UPCommonFunc_isValidString(familyId)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    [[UpFamilyPluginManager sharedInstance].userDomain.user setCurrentFamily:family];
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess];
    [callback onSuccess:[result toJsonObject]];
}
@end
