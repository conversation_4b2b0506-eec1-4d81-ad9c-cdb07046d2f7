//
//  UpModifyVirtualMemberRoleAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2022/8/24.
//

#import "UpModifyVirtualMemberRoleAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"

static NSString *const modifyVirtualMemberRole_ActionName = @"modifyVirtualMemberRoleForFamily";
@implementation UpModifyVirtualMemberRoleAction
+ (NSString *)action
{
    return modifyVirtualMemberRole_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *memberId = params[kFamilyPluginMemberId];
    NSString *memberRole = params[kFamilyPluginMemberRole];

    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(memberId) || !UPCommonFunc_isValidString(memberRole)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    [family modifyVirtualMemberRole:memberId
        memberRole:memberRole
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];

        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpModifyVirtualMemberRoleAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
