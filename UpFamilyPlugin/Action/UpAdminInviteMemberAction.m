//
//  UpAdminInvitateMemberAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/30.
//

#import "UpAdminInviteMemberAction.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>
#import <UPUserDomain/AdminInviteMemberArgs.h>

static NSString *const AdminInviteMember_ActionName = @"adminInviteMemberForFamily";
@implementation UpAdminInviteMemberAction
+ (NSString *)action
{
    return AdminInviteMember_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *userId = params[kFamilyPluginUserId];
    NSString *nickname = params[kFamilyPluginNickname];
    NSInteger memberType = [params[kFamilyPluginMemberType] integerValue];
    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(userId) || !UPCommonFunc_isValidString(nickname) || memberType < 1) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    AdminInviteMemberArgs *args = [[AdminInviteMemberArgs alloc] init];
    args.userId = userId;
    args.nickname = nickname;
    args.memberType = memberType;
    args.memberRole = params[kFamilyPluginMemberRole];

    [family adminInvitateMember:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpAdminInviteMemberAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
