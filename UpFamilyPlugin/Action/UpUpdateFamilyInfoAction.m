//
//  UpUpdateFamilyInAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpUpdateFamilyInfoAction.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>

static NSString *const UpdateFamilyInfo_ActionName = @"updateFamilyInfoForFamily";
@implementation UpUpdateFamilyInfoAction
+ (NSString *)action
{
    return UpdateFamilyInfo_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *familyName = params[kFamilyPluginFamilyName];
    NSString *familyPosition = StringTypeCheck(params[kFamilyPluginFamilyPosition]);
    NSDictionary *familyLocation = TypeCheck(NSDictionary.class, params[kFamilyPluginFamilyLocation], @{});
    NSString *longitude = TypeCheck(NSString.class, familyLocation[kFamilyPluginLongitude], @"0.0");
    NSString *latitude = TypeCheck(NSString.class, familyLocation[kFamilyPluginLatitude], @"0.0");
    NSString *cityCode = StringTypeCheck(familyLocation[kFamilyPluginCityCode]);

    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(familyName)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    FamilyArgs *familyArgs = [[FamilyArgs alloc] init];
    familyArgs.name = familyName;
    familyArgs.position = familyPosition;
    familyArgs.longitude = longitude;
    familyArgs.latitude = latitude;
    familyArgs.cityCode = cityCode;

    [family updateFamilyInfo:familyArgs
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];

        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpUpdateFamilyInfoAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
