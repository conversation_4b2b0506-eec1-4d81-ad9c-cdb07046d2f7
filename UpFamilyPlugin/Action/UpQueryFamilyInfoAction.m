//
//  UpQueryFamilyInfoAction.m
//  UpFamilyPlugin
//
//  Created by 吴子航 on 2022/1/18.
//

#import "UpQueryFamilyInfoAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"
#import "UpH5FamilyModel.h"

static NSString *const QueryFamilyInfo_ActionName = @"queryFamilyInfoForFamily";

@implementation UpQueryFamilyInfoAction

+ (NSString *)action
{
    return QueryFamilyInfo_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];

    if (!UPCommonFunc_isValidString(familyId)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    [family queryInfoSuccess:^(UserDomainSampleResult *_Nonnull result) {
      id<UDFamilyDelegate> family = result.retData;
      if (![family conformsToProtocol:@protocol(UDFamilyDelegate)]) {
          family = nil;
      }
      UpH5FamilyModel *familyModel = [[UpH5FamilyModel alloc] initWithFamilyModel:family];
      UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:[familyModel mj_keyValues]];
      [callback onSuccess:[commonResult toJsonObject]];
      UPLogDebug(@"UpFamilyPlugin", @"%s[%d]UpQueryFamilyInfoAction retData:%@", __PRETTY_FUNCTION__, __LINE__, commonResult.toJsonObject);
    }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpQueryFamilyInfoAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
