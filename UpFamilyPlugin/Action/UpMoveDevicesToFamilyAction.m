//
//  UpMoveDevicesToFamilyAction.m
//  UpFamilyPlugin
//
//  Created by 吴子航 on 2022/2/8.
//

#import "UpMoveDevicesToFamilyAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"
#import "BatchProcessDevice+UFPTools.h"

static NSString *const MoveDevicesToFamily_ActionName = @"moveDevicesToFamilyForFamily";

@implementation UpMoveDevicesToFamilyAction
+ (NSString *)action
{
    return MoveDevicesToFamily_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *newFamilyId = params[kFamilyPluginNewFamilyId];
    NSArray *devices = TypeCheck(NSArray.class, params[kFamilyPluginDeviceIds], @[]);
    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(newFamilyId) || devices.count == 0) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    id<UDFamilyDelegate> newFamily = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:newFamilyId];
    if (newFamily == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeTargetFamilyNotFound retInfo:kFamilyPluginRetInfoTargetFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    NSMutableArray *deviceArray = [NSMutableArray array];
    for (NSString *deviceId in devices) {
        id<UDDeviceDelegate> device = [[UpFamilyPluginManager sharedInstance].userDomain.user getDeviceById:deviceId];
        if (device) {
            [deviceArray addObject:device];
        }
    }

    if (deviceArray.count == 0) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeBatchDeviceNotFound retInfo:kFamilyPluginRetInfoBatchDeviceNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    [family moveDevicesToOtherFamily:newFamilyId
        devices:deviceArray
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          id retData = [NSNull null];
          if ([result.retData isKindOfClass:BatchProcessDeviceResult.class]) {
              retData = [(BatchProcessDeviceResult *)result.retData transformToPluginResult];
          }
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpMoveDevicesToFamilyAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
