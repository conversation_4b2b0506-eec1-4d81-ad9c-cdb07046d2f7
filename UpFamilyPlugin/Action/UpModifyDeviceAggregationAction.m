//
//  UpModifyDeviceAggregationAction.m
//  UpFamilyPlugin
//
//  Created by lubi<PERSON> on 2025/3/27.
//

#import "UpModifyDeviceAggregationAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UPCommonResult.h>
#import <uplog/UPLog.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <upuserdomain/DeviceCardAggregationArgs.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"

static NSString *const ModifyDeviceAggregation_ActionName = @"operateDeviceCardAggregationForFamily";

@implementation UpModifyDeviceAggregationAction

+ (NSString *)action
{
    return ModifyDeviceAggregation_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    if (!familyId) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [UpUserDomainHolder.instance.userDomain.user getFamilyById:familyId];
    if (!family) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    DeviceCardAggregationArgs *args = [[DeviceCardAggregationArgs alloc] init];
    args.familyId = familyId;
    NSArray<NSDictionary *> *cardsInfo = params[@"aggCard"];
    if (cardsInfo) {
        NSMutableArray<AggCard *> *aggCards = [NSMutableArray array];
        for (NSDictionary *info in cardsInfo) {
            AggCard *model = [AggCard mj_objectWithKeyValues:info];
            [aggCards addObject:model];
        }
        args.aggCard = aggCards;
    }

    [family modifyDeviceAggregation:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"UpFamilyPlugin", @"operateDeviceCardAggregationForFamily succecc:%@", result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"operateDeviceCardAggregationForFamily failure:%@", result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
