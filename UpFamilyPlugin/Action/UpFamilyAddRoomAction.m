//
//  UpFamilyAddRoomAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/30.
//

#import "UpFamilyAddRoomAction.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>

static NSString *const FamilyAddRoom_ActionName = @"familyAddRoomForFamily";
@implementation UpFamilyAddRoomAction
+ (NSString *)action
{
    return FamilyAddRoom_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *floorId = params[kFamilyPluginFloorId];
    NSString *roomName = params[kFamilyPluginRoomName];
    NSString *roomClass = params[kFamilyPluginRoomClass];
    NSString *roomLabel = params[kFamilyPluginRoomLabel];
    NSString *roomLogo = params[kFamilyPluginRoomLogo];
    NSString *roomPicture = params[kFamilyPluginRoomPicture];
    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(roomName) || !UPCommonFunc_isValidString(floorId)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    RoomArgs *room = [[RoomArgs alloc] init];
    room.name = roomName;
    room.floorId = floorId;
    room.type = StringTypeCheck(roomClass);
    room.label = StringTypeCheck(roomLabel);
    room.logo = StringTypeCheck(roomLogo);
    room.image = StringTypeCheck(roomPicture);

    [family addRoom:room
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          NSString *roomId = StringTypeCheck(result.retData);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:@{ @"roomId" : roomId }];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpFamilyAddRoomAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
