//
//  UpMoveDevicesToOtherRoomAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpMoveDevicesToOtherRoomAction.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>

static NSString *const MoveDevicesToOtherRoom_ActionName = @"moveDevicesToOtherRoomForFamily";
@implementation UpMoveDevicesToOtherRoomAction
+ (NSString *)action
{
    return MoveDevicesToOtherRoom_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *newRoomId = params[kFamilyPluginNewRoomId];
    NSArray *devices = TypeCheck(NSArray.class, params[kFamilyPluginDeviceIds], @[]);
    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(newRoomId) || devices.count == 0) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    id<UDRoomDelegate> room = [self getRoomWithRoomId:newRoomId fromFamily:family];
    if (room == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeRoomNotFound retInfo:kFamilyPluginRetInfoRoomNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    NSMutableArray *deviceArray = [NSMutableArray array];
    for (NSDictionary *deviceInfoDict in devices) {
        id<UDDeviceDelegate> device = [[UpFamilyPluginManager sharedInstance].userDomain.user getDeviceById:[deviceInfoDict objectForKey:@"deviceId"]];
        if (device) {
            [deviceArray addObject:device];
        }
    }

    [family moveDevicesToOtherRoom:room
        devices:deviceArray
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          id retData = [NSNull null];
          if ([result.retData isKindOfClass:BatchProcessDeviceResult.class]) {
              retData = [(BatchProcessDeviceResult *)result.retData mj_keyValues];
          }
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:retData];
          [callback onSuccess:[commonResult toJsonObject]];

        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpMoveDevicesToOtherRoomAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
- (id<UDRoomDelegate>)getRoomWithRoomId:(NSString *)roomId fromFamily:(id<UDFamilyDelegate>)family
{
    __block id<UDRoomDelegate> room = nil;
    [family.floorInfos enumerateObjectsUsingBlock:^(id<UDFloorInfoDelegate> _Nonnull floorInfo, NSUInteger idx, BOOL *_Nonnull stop) {
      [floorInfo.rooms enumerateObjectsUsingBlock:^(id<UDRoomDelegate> _Nonnull roomInfo, NSUInteger idx, BOOL *_Nonnull found) {
        if (roomId && [roomInfo.roomId isEqualToString:roomId]) {
            room = roomInfo;
            *found = YES;
            *stop = YES;
        }
      }];
    }];
    return room;
}
@end
