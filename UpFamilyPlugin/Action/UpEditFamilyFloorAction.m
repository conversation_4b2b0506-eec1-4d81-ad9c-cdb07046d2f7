//
//  UpEditFamilyFloorAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/30.
//

#import "UpEditFamilyFloorAction.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>

static NSString *const EditFamilyFloor_ActionName = @"editFamilyFloorForFamily";
@implementation UpEditFamilyFloorAction
+ (NSString *)action
{
    return EditFamilyFloor_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    NSString *floorId = params[kFamilyPluginFloorId];
    NSString *floorOrderId = params[kFamilyPluginFloorOrderId];
    NSString *floorName = params[kFamilyPluginFloorName];
    NSString *floorLabel = params[kFamilyPluginFloorLabel];
    NSString *floorLogo = params[kFamilyPluginFloorLogo];
    NSString *floorPicture = params[kFamilyPluginFloorPicture];
    if (!UPCommonFunc_isValidString(familyId) || !UPCommonFunc_isValidString(floorId)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    id<UDFamilyDelegate> family = [[UpFamilyPluginManager sharedInstance].userDomain.user getFamilyById:familyId];
    if (family == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    id<UDFloorInfoDelegate> floor = [self getFloorWithFloorId:floorId fromFamily:family];
    if (nil == floor) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFloorNotFound retInfo:kFamilyPluginRetInfoFloorNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    FloorArg *floorArg = [[FloorArg alloc] initFloorArg:floor];
    floorArg.floorOrderId = UPCommonFunc_isValidString(floorOrderId) ? floorOrderId : floorArg.floorOrderId;
    floorArg.floorName = UPCommonFunc_isValidString(floorName) ? floorName : floorArg.floorName;
    floorArg.floorLabel = UPCommonFunc_isValidString(floorLabel) ? floorLabel : floorArg.floorLabel;
    floorArg.floorLogo = UPCommonFunc_isValidString(floorLogo) ? floorLogo : floorArg.floorLogo;
    floorArg.floorPicture = UPCommonFunc_isValidString(floorPicture) ? floorPicture : floorArg.floorPicture;

    [family editFloor:floorArg
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpEditFamilyFloorAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues)
              UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

- (id<UDFloorInfoDelegate>)getFloorWithFloorId:(NSString *)floorId fromFamily:(id<UDFamilyDelegate>)family
{
    __block id<UDFloorInfoDelegate> floor = nil;
    [family.floorInfos enumerateObjectsUsingBlock:^(id<UDFloorInfoDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (floorId && [obj.floorId isEqualToString:floorId]) {
          floor = obj;
          *stop = YES;
      }
    }];
    return floor;
}
@end
