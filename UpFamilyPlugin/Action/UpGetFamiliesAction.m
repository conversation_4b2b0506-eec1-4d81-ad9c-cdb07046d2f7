//
//  UpGetFamiliesAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/27.
//

#import "UpGetFamiliesAction.h"
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFlutterFamilyModel.h"
#import <MJExtension/MJExtension.h>
#import "UpH5FamilyModel.h"
#import "UpFamilyPluginManager.h"

static NSString *const GetFamilies_ActionName = @"getFamiliesForFamily";
@implementation UpGetFamiliesAction
+ (NSString *)action
{
    return GetFamilies_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    UpUserDomainState state = [UpFamilyPluginManager sharedInstance].userDomain.state;
    if (state == UpUserDomainStateUnLogin) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeUserNotLogin retInfo:kFamilyPluginRetInfoUserNotLogin];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    if (![UpFamilyPluginManager sharedInstance].userDomain.isRefreshFamilyListCompleted) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeUserDataNotRefresh retInfo:kFamilyPluginRetInfoUserDataNotRefresh];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    User *user = (User *)[UpFamilyPluginManager sharedInstance].userDomain.user;

    if (self.callerType == CallMethodPlatformFlutter) {
        NSMutableDictionary *familyMap = [NSMutableDictionary dictionary];
        [user.families enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, id<UDFamilyDelegate> _Nonnull obj, BOOL *_Nonnull stop) {
          UpFlutterFamilyModel *familyModel = [[UpFlutterFamilyModel alloc] initWithFamilyModel:obj];
          familyMap[key] = [familyModel mj_keyValues];
        }];
        UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:familyMap];
        [callback onSuccess:[result toJsonObject]];
    }
    else {
        NSMutableArray *families = [NSMutableArray array];
        [user.families enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, id<UDFamilyDelegate> _Nonnull obj, BOOL *_Nonnull stop) {
          UpH5FamilyModel *familyModel = [[UpH5FamilyModel alloc] initWithFamilyModel:obj];
          NSDictionary *familyModelDict = [familyModel mj_keyValues];
          if (familyModelDict) {
              [families addObject:familyModelDict];
          }
        }];
        UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:families];
        [callback onSuccess:[result toJsonObject]];
    }
}


@end
