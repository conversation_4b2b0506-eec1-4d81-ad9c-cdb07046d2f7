//
//  UpCreateFamilyAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpCreateFamilyAction.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>

static NSString *const CreateFamily_ActionName = @"createFamilyForFamily";
@implementation UpCreateFamilyAction
+ (NSString *)action
{
    return CreateFamily_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyName = params[kFamilyPluginFamilyName];
    NSString *familyPosition = params[kFamilyPluginFamilyPosition];
    NSDictionary *familyLocation = TypeCheck(NSDictionary.class, params[kFamilyPluginFamilyLocation], @{});
    NSString *longitude = [TypeCheck(NSNumber.class, familyLocation[kFamilyPluginLongitude], @0.0) stringValue];
    NSString *latitude = [TypeCheck(NSNumber.class, familyLocation[kFamilyPluginLatitude], @0.0) stringValue];
    NSString *cityCode = familyLocation[kFamilyPluginCityCode];
    NSArray *roomNames = params[kFamilyPluginRoomNames];
    if (!UPCommonFunc_isValidString(familyName)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    CreateFamilyArgs *familyInfo = [CreateFamilyArgs new];
    familyInfo.name = familyName;
    familyInfo.position = familyPosition;
    familyInfo.longitude = longitude;
    familyInfo.latitude = latitude;
    familyInfo.cityCode = cityCode;
    familyInfo.roomNames = roomNames;
    [[UpFamilyPluginManager sharedInstance]
            .userDomain.user createFamily:familyInfo
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          NSString *familyId = result.retData;
          NSDictionary *dict = @{ @"familyId" : StringTypeCheck(familyId) };
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:dict];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpCreateFamilyAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}
@end
