//
//  UpUpdateAndCheckDeviceNameAction.m
//  UpFamilyPlugin
//
//  Created by whenwe on 2023/9/22.
//

#import "UpUpdateAndCheckDeviceNameAction.h"
#import <MJExtension/MJExtension.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"

static NSString *const UpUpdateAndCheckDeviceName_ActionName = @"updateAndCheckDeviceNameForFamily";

@implementation UpUpdateAndCheckDeviceNameAction

+ (NSString *)action
{
    return UpUpdateAndCheckDeviceName_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *deviceId = params[kFamilyPluginDeviceId];
    NSString *deviceName = params[kFamilyPluginDeviceName];
    NSString *type = params[kFamilyPluginType];
    NSNumber *checkLevelNum = params[kFamilyPluginCheckLevel];
    if (!UPCommonFunc_isValidString(deviceId) || !UPCommonFunc_isValidString(deviceName) || !UPCommonFunc_isValidString(type)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    id<UDDeviceDelegate> device = [[UpFamilyPluginManager sharedInstance].userDomain.user getDeviceById:deviceId];
    if (device == nil) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeDeviceNotFound retInfo:kFamilyPluginRetInfoDeviceNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    BOOL checkLevel = YES;
    if ([checkLevelNum isKindOfClass:[NSNumber class]]) {
        checkLevel = [checkLevelNum boolValue];
    }
    [device updateDeviceName:deviceName
        checkLevel:checkLevel
        type:type
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogInfo(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          NSString *deviceId = StringTypeCheck(result.retData);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:@{ @"deviceId" : deviceId }];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]UpUpdateAndCheckDeviceNameAction failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
