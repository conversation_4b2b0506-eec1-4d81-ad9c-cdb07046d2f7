//
//  UpConfirmDeviceSharingRelationAction.m
//  UpFamilyPlugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/13.
//

#import "UpConfirmDeviceSharingRelationAction.h"
#import <MJExtension/MJExtension.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPLog/UPLog.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import "UpFamilyPluginManager.h"
static NSString *const ConfirmDeviceSharingRelation_ActionName = @"confirmDeviceSharingRelationForFamily";
@implementation UpConfirmDeviceSharingRelationAction
+ (NSString *)action
{
    return ConfirmDeviceSharingRelation_ActionName;
}
- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *shareUuid = params[kFamilyPluginShareUuid];
    if (!UPCommonFunc_isValidString(shareUuid)) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }
    [[UpFamilyPluginManager sharedInstance]
            .userDomain.user confirmDeviceSharingRelation:shareUuid
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"UpFamilyPlugin", @"%s[%d]UpFamilyPlugin retData:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];

        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"%s[%d]confirmDeviceSharingRelationForFamily failure:%@", __PRETTY_FUNCTION__, __LINE__, result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
