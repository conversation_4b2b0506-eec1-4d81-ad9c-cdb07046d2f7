//
//  UpModifyDeviceCardStatusAction.m
//  UpFamilyPlugin
//
//  Created by lubiao on 2025/3/27.
//

#import "UpModifyDeviceCardStatusAction.h"
#import <MJExtension/MJExtension.h>
#import <uplog/UPLog.h>
#import <UpPluginFoundation/UPCommonResult.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import <upuserdomain/DeviceCardStatusArgs.h>
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"

static NSString *const ModifyDeviceCardStatus_ActionName = @"operateDeviceCardStatusForFamily";

@implementation UpModifyDeviceCardStatusAction

+ (NSString *)action
{
    return ModifyDeviceCardStatus_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *familyId = params[kFamilyPluginFamilyId];
    if (!familyId) {
        [UpFamilyPluginUtil callBackResultParamsVerify:callback params:params];
        return;
    }

    id<UDFamilyDelegate> family = [UpUserDomainHolder.instance.userDomain.user getFamilyById:familyId];
    if (!family) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeFamilyNotFound retInfo:kFamilyPluginRetInfoFamilyNotFound];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    DeviceCardStatusArgs *args = [DeviceCardStatusArgs mj_objectWithKeyValues:params];

    [family modifyDeviceCardStatus:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          UPLogDebug(@"UpFamilyPlugin", @"operateDeviceCardStatusForFamily succecc:%@", result.mj_keyValues);
          UPCommonResult *commonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:result.retData];
          [callback onSuccess:[commonResult toJsonObject]];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          UPLogError(@"UpFamilyPlugin", @"operateDeviceCardStatusForFamily failure:%@", result.mj_keyValues);
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:result.retCode retInfo:result.retInfo];
          [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        }];
}

@end
