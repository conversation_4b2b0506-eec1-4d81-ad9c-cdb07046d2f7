//
//  UpGetCurrentFamilyAction.m
//  UpFamilyPlugin
//
//  Created by 冉东军 on 2021/8/31.
//

#import "UpGetCurrentFamilyAction.h"
#import "UpFamilyDeclaration.h"
#import "UpFamilyPluginUtil.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpFamilyPluginManager.h"
#import <UPLog/UPLog.h>
#import "UpFlutterFamilyModel.h"
#import "UpH5FamilyModel.h"
#import <MJExtension/MJExtension.h>

static NSString *const GetCurrentFamily_ActionName = @"getCurrentFamilyForFamily";
@implementation UpGetCurrentFamilyAction
+ (NSString *)action
{
    return GetCurrentFamily_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    UpUserDomainState state = [UpFamilyPluginManager sharedInstance].userDomain.state;
    if (state == UpUserDomainStateUnLogin) {
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeUserNotLogin retInfo:kFamilyPluginRetInfoUserNotLogin];
        [callback onFailure:kUPCommonResult.retCode errMessage:kUPCommonResult.retInfo details:[kUPCommonResult toJsonObject]];
        return;
    }
    Family *family = (Family *)[UpFamilyPluginManager sharedInstance].userDomain.user.currentFamily;
    if (family) {
        [self sendFamilyData:family toCallback:callback];
        return;
    }
    [[UpFamilyPluginManager sharedInstance]
            .userDomain.user refreshFamilyList:^(UserDomainSampleResult *_Nonnull res) {
      Family *family = (Family *)[UpFamilyPluginManager sharedInstance].userDomain.user.currentFamily;
      if (family) {
          [self sendFamilyData:family toCallback:callback];
      }
      else {
          [self sendEmptyFamilyDataToCallback:callback];
      }
    }
        failure:^(UserDomainSampleResult *_Nonnull res) {
          [self sendEmptyFamilyDataToCallback:callback];
        }];
}

#pragma mark - private
- (void)sendFamilyData:(Family *)family
            toCallback:(id<UPPCallBackProtocol>)callback
{

    NSDictionary *currentFamilies = [NSDictionary dictionary];
    if (self.callerType == CallMethodPlatformFlutter) {
        UpFlutterFamilyModel *familyModel = [[UpFlutterFamilyModel alloc] initWithFamilyModel:family];
        currentFamilies = [familyModel mj_keyValues];
    }
    else {
        UpH5FamilyModel *familyModel = [[UpH5FamilyModel alloc] initWithFamilyModel:family];
        currentFamilies = [familyModel mj_keyValues];
    }
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess retInfo:kFamilyPluginRetInfoSuccess withData:currentFamilies];
    UPLogDebug(@"UpFamilyPlugin", @"getCurrentFamilyForFamily success:%@", result.mj_keyValues);
    [callback onSuccess:[result toJsonObject]];
}

- (void)sendEmptyFamilyDataToCallback:(id<UPPCallBackProtocol>)callback
{
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:kFamilyPluginRetCodeSuccess
                                                             retInfo:kFamilyPluginRetInfoSuccess
                                                            withData:@{}];
    UPLogDebug(@"UpFamilyPlugin", @"getCurrentFamilyForFamily empty:%@", result.mj_keyValues);
    [callback onSuccess:[result toJsonObject]];
}
@end
