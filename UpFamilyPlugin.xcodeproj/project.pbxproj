// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		12F9D2155A7C340D8FB841EB /* libPods-UpFamilyPlugin-UpFamilyPluginTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42E156175919D5F219C6AC4A /* libPods-UpFamilyPlugin-UpFamilyPluginTests.a */; };
		221019D126E6F731004D0295 /* NSString+NumberParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 221019CF26E6F731004D0295 /* NSString+NumberParser.m */; };
		22157D0426E9D66000311498 /* UpPluginFamilyTestHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = 22157D0326E9D66000311498 /* UpPluginFamilyTestHolder.m */; };
		22189C5E292239D40086193F /* UpSaveRoomsOrderAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22189C5D292239D40086193F /* UpSaveRoomsOrderAction.m */; };
		223223F528B5FE9D00A7A0E7 /* UpModifyVirtualMemberRoleAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 223223F428B5FE9D00A7A0E7 /* UpModifyVirtualMemberRoleAction.m */; };
		224AA1A526E1B78E005D552D /* UpCommonFamilyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1A426E1B78E005D552D /* UpCommonFamilyModel.m */; };
		224AA1AA26E5A607005D552D /* ActionSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1A826E5A606005D552D /* ActionSteps.m */; };
		224AA1AD26E5A650005D552D /* InitSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1AC26E5A650005D552D /* InitSteps.m */; };
		224AA1B026E5AAC5005D552D /* StepsCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1AF26E5AAC5005D552D /* StepsCacheData.m */; };
		224AA1B326E5ABBA005D552D /* StepsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1B226E5ABBA005D552D /* StepsUtils.m */; };
		224AA1BB26E5AC35005D552D /* NSArray+Lite.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1B726E5AC35005D552D /* NSArray+Lite.m */; };
		224AA1BC26E5AC35005D552D /* NSDictionary+SubMap.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1B926E5AC35005D552D /* NSDictionary+SubMap.m */; };
		224AA1BF26E5AC4C005D552D /* UPUnitTestCallBackIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = 224AA1BE26E5AC4C005D552D /* UPUnitTestCallBackIMP.m */; };
		224AA1C126E5AE6F005D552D /* features in Resources */ = {isa = PBXBuildFile; fileRef = 224AA1C026E5AE6F005D552D /* features */; };
		225213A028A2522F001D2832 /* UpModifyMemberRoleAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 2252139F28A2522F001D2832 /* UpModifyMemberRoleAction.m */; };
		225658C526DC6AA50045BF18 /* UpEditFamilyFloorAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658C426DC6AA50045BF18 /* UpEditFamilyFloorAction.m */; };
		225658C826DC6AD80045BF18 /* UpAdminInviteMemberAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658C726DC6AD80045BF18 /* UpAdminInviteMemberAction.m */; };
		225658CB26DC6AFC0045BF18 /* UpDeleteFamilyFloorAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658CA26DC6AFC0045BF18 /* UpDeleteFamilyFloorAction.m */; };
		225658CE26DC6B270045BF18 /* UpCreateFamilyFloorAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658CD26DC6B270045BF18 /* UpCreateFamilyFloorAction.m */; };
		225658D126DC6B4E0045BF18 /* UpFamilyAddRoomAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658D026DC6B4E0045BF18 /* UpFamilyAddRoomAction.m */; };
		225658D526DC6FBF0045BF18 /* UpFamilyPluginUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658D426DC6FBF0045BF18 /* UpFamilyPluginUtil.m */; };
		225658DB26DDBD5F0045BF18 /* UpFlutterFamilyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658DA26DDBD5F0045BF18 /* UpFlutterFamilyModel.m */; };
		225658DE26DDBD7C0045BF18 /* UpH5FamilyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 225658DD26DDBD7C0045BF18 /* UpH5FamilyModel.m */; };
		22C9610E26DDFAD1001A4CA7 /* UpGetCurrentFamilyAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9610D26DDFAD1001A4CA7 /* UpGetCurrentFamilyAction.m */; };
		22C9611126DDFB02001A4CA7 /* UpSetCurrentFamilyAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9611026DDFB02001A4CA7 /* UpSetCurrentFamilyAction.m */; };
		22C9611426DDFB1E001A4CA7 /* UpCreateFamilyAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9611326DDFB1E001A4CA7 /* UpCreateFamilyAction.m */; };
		22C9611726DDFE4B001A4CA7 /* UpUpdateFamilyInfoAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9611626DDFE4B001A4CA7 /* UpUpdateFamilyInfoAction.m */; };
		22C9611A26DDFE86001A4CA7 /* UpChangeFamilyAdminAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9611926DDFE86001A4CA7 /* UpChangeFamilyAdminAction.m */; };
		22C9611D26DE0062001A4CA7 /* UpExitFamilyAsMemberAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9611C26DE0062001A4CA7 /* UpExitFamilyAsMemberAction.m */; };
		22C9612026DE0098001A4CA7 /* UpExitFamilyAsAdminAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9611F26DE0098001A4CA7 /* UpExitFamilyAsAdminAction.m */; };
		22C9612326DE00C6001A4CA7 /* UpDeleteFamilyAsAdminAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9612226DE00C6001A4CA7 /* UpDeleteFamilyAsAdminAction.m */; };
		22C9612626DE0102001A4CA7 /* UpUpdateDeviceNameAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9612526DE0102001A4CA7 /* UpUpdateDeviceNameAction.m */; };
		22C9612926DE02CF001A4CA7 /* UpRefreshRoomListAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9612826DE02CF001A4CA7 /* UpRefreshRoomListAction.m */; };
		22C9612C26DE02FE001A4CA7 /* UpFamilyRemoveRoomAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9612B26DE02FE001A4CA7 /* UpFamilyRemoveRoomAction.m */; };
		22C9612F26DE0343001A4CA7 /* UpUpdateRoomNameAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9612E26DE0343001A4CA7 /* UpUpdateRoomNameAction.m */; };
		22C9613226DE0377001A4CA7 /* UpMoveDevicesToOtherRoomAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9613126DE0377001A4CA7 /* UpMoveDevicesToOtherRoomAction.m */; };
		22C9613526DE03A6001A4CA7 /* UpAdminDeleteMemberAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C9613426DE03A6001A4CA7 /* UpAdminDeleteMemberAction.m */; };
		22EBB88C26D8CAB200C93D9B /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB88B26D8CAB200C93D9B /* AppDelegate.m */; };
		22EBB88F26D8CAB200C93D9B /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB88E26D8CAB200C93D9B /* SceneDelegate.m */; };
		22EBB89226D8CAB200C93D9B /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB89126D8CAB200C93D9B /* ViewController.m */; };
		22EBB89526D8CAB200C93D9B /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 22EBB89326D8CAB200C93D9B /* Main.storyboard */; };
		22EBB89726D8CAB300C93D9B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 22EBB89626D8CAB300C93D9B /* Assets.xcassets */; };
		22EBB89A26D8CAB300C93D9B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 22EBB89826D8CAB300C93D9B /* LaunchScreen.storyboard */; };
		22EBB89D26D8CAB300C93D9B /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB89C26D8CAB300C93D9B /* main.m */; };
		22EBB8A826D8CB1D00C93D9B /* UpFamilyPluginTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB8A726D8CB1D00C93D9B /* UpFamilyPluginTests.m */; };
		22EBB8AA26D8CB1D00C93D9B /* libUpFamilyPlugin.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 22EBB87826D8CA7200C93D9B /* libUpFamilyPlugin.a */; };
		22EBB8B526D8D68100C93D9B /* CucumberRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB8B426D8D68100C93D9B /* CucumberRunner.m */; };
		22EBB8BE26D8DDC000C93D9B /* UpFamilyDeclaration.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB8BD26D8DDC000C93D9B /* UpFamilyDeclaration.m */; };
		22EBB8C126D8DE8600C93D9B /* UpFamilyPluginManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB8C026D8DE8600C93D9B /* UpFamilyPluginManager.m */; };
		22EBB8C426D8E07500C93D9B /* UpGetFamiliesAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBB8C326D8E07500C93D9B /* UpGetFamiliesAction.m */; };
		2C29A5342817E88100236CF9 /* UpRemoveDeviceFromFamilyAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C29A5332817E88100236CF9 /* UpRemoveDeviceFromFamilyAction.m */; };
		84F59F9C2ABD861E00E88D5A /* UpUpdateAndCheckDeviceNameAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 84F59F9B2ABD861E00E88D5A /* UpUpdateAndCheckDeviceNameAction.m */; };
		950119EE2DA9171800F92F77 /* UpModifyMemberTypeAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 950119ED2DA9171800F92F77 /* UpModifyMemberTypeAction.m */; };
		950119FC2DA92EA400F92F77 /* UpFamilyAddRoomNewAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 950119FB2DA92EA400F92F77 /* UpFamilyAddRoomNewAction.m */; };
		95D74BF52D94E9D200086E12 /* UpModifyDeviceCardStatusAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 95D74BF42D94E9D200086E12 /* UpModifyDeviceCardStatusAction.m */; };
		95D74BF82D94EFE100086E12 /* UpModifyDeviceAggregationAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 95D74BF72D94EFE100086E12 /* UpModifyDeviceAggregationAction.m */; };
		95D74BFB2D94F3E500086E12 /* UpModifyAggregationSwitchAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 95D74BFA2D94F3E500086E12 /* UpModifyAggregationSwitchAction.m */; };
		95EC2CB22DB6382A00E22D0E /* UpSaveRoomsOrderNewAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 95EC2CB12DB6382A00E22D0E /* UpSaveRoomsOrderNewAction.m */; };
		983DD52C84770B09B00A3C26 /* libPods-UpFamilyPlugin.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 393F867A0902F92C6F671DCE /* libPods-UpFamilyPlugin.a */; };
		F7CBB36B27964937007A713D /* UpQueryFirstMemberAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F7CBB36A27964937007A713D /* UpQueryFirstMemberAction.m */; };
		F7CBB36E2796495D007A713D /* UpQueryFamilyInfoAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F7CBB36D2796495D007A713D /* UpQueryFamilyInfoAction.m */; };
		F7CBB3712796497F007A713D /* UpAddVirtualMemberAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F7CBB3702796497F007A713D /* UpAddVirtualMemberAction.m */; };
		F7CBB374279649AA007A713D /* UpModifyVirtualMemberAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F7CBB373279649AA007A713D /* UpModifyVirtualMemberAction.m */; };
		F7E55AB627B1FE7500121DC9 /* UpMoveDevicesToFamilyAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F7E55AB527B1FE7500121DC9 /* UpMoveDevicesToFamilyAction.m */; };
		F7E55AB927B1FE8F00121DC9 /* UpUnbindDeviceFromFamilyAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F7E55AB827B1FE8F00121DC9 /* UpUnbindDeviceFromFamilyAction.m */; };
		F7E55ABD27B206C300121DC9 /* BatchProcessDevice+UFPTools.m in Sources */ = {isa = PBXBuildFile; fileRef = F7E55ABC27B206C300121DC9 /* BatchProcessDevice+UFPTools.m */; };
		F8AEC6A62D826C51008C92C5 /* UpConfirmDeviceSharingRelationAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC6A52D826C51008C92C5 /* UpConfirmDeviceSharingRelationAction.m */; };
		F8AEC6A92D8272A8008C92C5 /* UpCancelDeviceSharingRelationAction.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC6A82D8272A8008C92C5 /* UpCancelDeviceSharingRelationAction.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		22EBB8AB26D8CB1D00C93D9B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 22EBB87026D8CA7200C93D9B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 22EBB87726D8CA7200C93D9B;
			remoteInfo = UpFamilyPlugin;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		22EBB87626D8CA7200C93D9B /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		221019CF26E6F731004D0295 /* NSString+NumberParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+NumberParser.m"; sourceTree = "<group>"; };
		221019D026E6F731004D0295 /* NSString+NumberParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+NumberParser.h"; sourceTree = "<group>"; };
		22157D0226E9D66000311498 /* UpPluginFamilyTestHolder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpPluginFamilyTestHolder.h; sourceTree = "<group>"; };
		22157D0326E9D66000311498 /* UpPluginFamilyTestHolder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpPluginFamilyTestHolder.m; sourceTree = "<group>"; };
		22189C5C292239D40086193F /* UpSaveRoomsOrderAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSaveRoomsOrderAction.h; sourceTree = "<group>"; };
		22189C5D292239D40086193F /* UpSaveRoomsOrderAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSaveRoomsOrderAction.m; sourceTree = "<group>"; };
		223223F328B5FE9D00A7A0E7 /* UpModifyVirtualMemberRoleAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpModifyVirtualMemberRoleAction.h; sourceTree = "<group>"; };
		223223F428B5FE9D00A7A0E7 /* UpModifyVirtualMemberRoleAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpModifyVirtualMemberRoleAction.m; sourceTree = "<group>"; };
		224AA1A326E1B78E005D552D /* UpCommonFamilyModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCommonFamilyModel.h; sourceTree = "<group>"; };
		224AA1A426E1B78E005D552D /* UpCommonFamilyModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCommonFamilyModel.m; sourceTree = "<group>"; };
		224AA1A826E5A606005D552D /* ActionSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ActionSteps.m; sourceTree = "<group>"; };
		224AA1A926E5A606005D552D /* ActionSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ActionSteps.h; sourceTree = "<group>"; };
		224AA1AB26E5A650005D552D /* InitSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InitSteps.h; sourceTree = "<group>"; };
		224AA1AC26E5A650005D552D /* InitSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InitSteps.m; sourceTree = "<group>"; };
		224AA1AE26E5AAC5005D552D /* StepsCacheData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StepsCacheData.h; sourceTree = "<group>"; };
		224AA1AF26E5AAC5005D552D /* StepsCacheData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StepsCacheData.m; sourceTree = "<group>"; };
		224AA1B126E5ABBA005D552D /* StepsUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StepsUtils.h; sourceTree = "<group>"; };
		224AA1B226E5ABBA005D552D /* StepsUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StepsUtils.m; sourceTree = "<group>"; };
		224AA1B626E5AC35005D552D /* NSDictionary+SubMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+SubMap.h"; sourceTree = "<group>"; };
		224AA1B726E5AC35005D552D /* NSArray+Lite.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSArray+Lite.m"; sourceTree = "<group>"; };
		224AA1B826E5AC35005D552D /* NSArray+Lite.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+Lite.h"; sourceTree = "<group>"; };
		224AA1B926E5AC35005D552D /* NSDictionary+SubMap.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+SubMap.m"; sourceTree = "<group>"; };
		224AA1BD26E5AC4C005D552D /* UPUnitTestCallBackIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUnitTestCallBackIMP.h; sourceTree = "<group>"; };
		224AA1BE26E5AC4C005D552D /* UPUnitTestCallBackIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUnitTestCallBackIMP.m; sourceTree = "<group>"; };
		224AA1C026E5AE6F005D552D /* features */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = features; sourceTree = SOURCE_ROOT; };
		2252139E28A2522F001D2832 /* UpModifyMemberRoleAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpModifyMemberRoleAction.h; sourceTree = "<group>"; };
		2252139F28A2522F001D2832 /* UpModifyMemberRoleAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpModifyMemberRoleAction.m; sourceTree = "<group>"; };
		225658C326DC6AA50045BF18 /* UpEditFamilyFloorAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEditFamilyFloorAction.h; sourceTree = "<group>"; };
		225658C426DC6AA50045BF18 /* UpEditFamilyFloorAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpEditFamilyFloorAction.m; sourceTree = "<group>"; };
		225658C626DC6AD80045BF18 /* UpAdminInviteMemberAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAdminInviteMemberAction.h; sourceTree = "<group>"; };
		225658C726DC6AD80045BF18 /* UpAdminInviteMemberAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAdminInviteMemberAction.m; sourceTree = "<group>"; };
		225658C926DC6AFC0045BF18 /* UpDeleteFamilyFloorAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeleteFamilyFloorAction.h; sourceTree = "<group>"; };
		225658CA26DC6AFC0045BF18 /* UpDeleteFamilyFloorAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeleteFamilyFloorAction.m; sourceTree = "<group>"; };
		225658CC26DC6B270045BF18 /* UpCreateFamilyFloorAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCreateFamilyFloorAction.h; sourceTree = "<group>"; };
		225658CD26DC6B270045BF18 /* UpCreateFamilyFloorAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCreateFamilyFloorAction.m; sourceTree = "<group>"; };
		225658CF26DC6B4E0045BF18 /* UpFamilyAddRoomAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFamilyAddRoomAction.h; sourceTree = "<group>"; };
		225658D026DC6B4E0045BF18 /* UpFamilyAddRoomAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyAddRoomAction.m; sourceTree = "<group>"; };
		225658D326DC6FBF0045BF18 /* UpFamilyPluginUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFamilyPluginUtil.h; sourceTree = "<group>"; };
		225658D426DC6FBF0045BF18 /* UpFamilyPluginUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyPluginUtil.m; sourceTree = "<group>"; };
		225658D926DDBD5F0045BF18 /* UpFlutterFamilyModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFlutterFamilyModel.h; sourceTree = "<group>"; };
		225658DA26DDBD5F0045BF18 /* UpFlutterFamilyModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFlutterFamilyModel.m; sourceTree = "<group>"; };
		225658DC26DDBD7C0045BF18 /* UpH5FamilyModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpH5FamilyModel.h; sourceTree = "<group>"; };
		225658DD26DDBD7C0045BF18 /* UpH5FamilyModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpH5FamilyModel.m; sourceTree = "<group>"; };
		22C9610C26DDFAD1001A4CA7 /* UpGetCurrentFamilyAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpGetCurrentFamilyAction.h; sourceTree = "<group>"; };
		22C9610D26DDFAD1001A4CA7 /* UpGetCurrentFamilyAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpGetCurrentFamilyAction.m; sourceTree = "<group>"; };
		22C9610F26DDFB02001A4CA7 /* UpSetCurrentFamilyAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSetCurrentFamilyAction.h; sourceTree = "<group>"; };
		22C9611026DDFB02001A4CA7 /* UpSetCurrentFamilyAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSetCurrentFamilyAction.m; sourceTree = "<group>"; };
		22C9611226DDFB1E001A4CA7 /* UpCreateFamilyAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCreateFamilyAction.h; sourceTree = "<group>"; };
		22C9611326DDFB1E001A4CA7 /* UpCreateFamilyAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCreateFamilyAction.m; sourceTree = "<group>"; };
		22C9611526DDFE4B001A4CA7 /* UpUpdateFamilyInfoAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUpdateFamilyInfoAction.h; sourceTree = "<group>"; };
		22C9611626DDFE4B001A4CA7 /* UpUpdateFamilyInfoAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUpdateFamilyInfoAction.m; sourceTree = "<group>"; };
		22C9611826DDFE86001A4CA7 /* UpChangeFamilyAdminAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpChangeFamilyAdminAction.h; sourceTree = "<group>"; };
		22C9611926DDFE86001A4CA7 /* UpChangeFamilyAdminAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpChangeFamilyAdminAction.m; sourceTree = "<group>"; };
		22C9611B26DE0062001A4CA7 /* UpExitFamilyAsMemberAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpExitFamilyAsMemberAction.h; sourceTree = "<group>"; };
		22C9611C26DE0062001A4CA7 /* UpExitFamilyAsMemberAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpExitFamilyAsMemberAction.m; sourceTree = "<group>"; };
		22C9611E26DE0098001A4CA7 /* UpExitFamilyAsAdminAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpExitFamilyAsAdminAction.h; sourceTree = "<group>"; };
		22C9611F26DE0098001A4CA7 /* UpExitFamilyAsAdminAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpExitFamilyAsAdminAction.m; sourceTree = "<group>"; };
		22C9612126DE00C6001A4CA7 /* UpDeleteFamilyAsAdminAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeleteFamilyAsAdminAction.h; sourceTree = "<group>"; };
		22C9612226DE00C6001A4CA7 /* UpDeleteFamilyAsAdminAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeleteFamilyAsAdminAction.m; sourceTree = "<group>"; };
		22C9612426DE0102001A4CA7 /* UpUpdateDeviceNameAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUpdateDeviceNameAction.h; sourceTree = "<group>"; };
		22C9612526DE0102001A4CA7 /* UpUpdateDeviceNameAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUpdateDeviceNameAction.m; sourceTree = "<group>"; };
		22C9612726DE02CF001A4CA7 /* UpRefreshRoomListAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpRefreshRoomListAction.h; sourceTree = "<group>"; };
		22C9612826DE02CF001A4CA7 /* UpRefreshRoomListAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpRefreshRoomListAction.m; sourceTree = "<group>"; };
		22C9612A26DE02FE001A4CA7 /* UpFamilyRemoveRoomAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFamilyRemoveRoomAction.h; sourceTree = "<group>"; };
		22C9612B26DE02FE001A4CA7 /* UpFamilyRemoveRoomAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyRemoveRoomAction.m; sourceTree = "<group>"; };
		22C9612D26DE0343001A4CA7 /* UpUpdateRoomNameAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUpdateRoomNameAction.h; sourceTree = "<group>"; };
		22C9612E26DE0343001A4CA7 /* UpUpdateRoomNameAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUpdateRoomNameAction.m; sourceTree = "<group>"; };
		22C9613026DE0377001A4CA7 /* UpMoveDevicesToOtherRoomAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpMoveDevicesToOtherRoomAction.h; sourceTree = "<group>"; };
		22C9613126DE0377001A4CA7 /* UpMoveDevicesToOtherRoomAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpMoveDevicesToOtherRoomAction.m; sourceTree = "<group>"; };
		22C9613326DE03A6001A4CA7 /* UpAdminDeleteMemberAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAdminDeleteMemberAction.h; sourceTree = "<group>"; };
		22C9613426DE03A6001A4CA7 /* UpAdminDeleteMemberAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAdminDeleteMemberAction.m; sourceTree = "<group>"; };
		22EBB87826D8CA7200C93D9B /* libUpFamilyPlugin.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUpFamilyPlugin.a; sourceTree = BUILT_PRODUCTS_DIR; };
		22EBB88826D8CAB200C93D9B /* UpFamilyPluginDebug.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = UpFamilyPluginDebug.app; sourceTree = BUILT_PRODUCTS_DIR; };
		22EBB88A26D8CAB200C93D9B /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		22EBB88B26D8CAB200C93D9B /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		22EBB88D26D8CAB200C93D9B /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		22EBB88E26D8CAB200C93D9B /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		22EBB89026D8CAB200C93D9B /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		22EBB89126D8CAB200C93D9B /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		22EBB89426D8CAB200C93D9B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		22EBB89626D8CAB300C93D9B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		22EBB89926D8CAB300C93D9B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		22EBB89B26D8CAB300C93D9B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		22EBB89C26D8CAB300C93D9B /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		22EBB8A526D8CB1D00C93D9B /* UpFamilyPluginTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UpFamilyPluginTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		22EBB8A726D8CB1D00C93D9B /* UpFamilyPluginTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyPluginTests.m; sourceTree = "<group>"; };
		22EBB8A926D8CB1D00C93D9B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		22EBB8B126D8CEAD00C93D9B /* readme.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = readme.md; sourceTree = "<group>"; };
		22EBB8B226D8CEAD00C93D9B /* release.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = release.md; sourceTree = "<group>"; };
		22EBB8B326D8CECE00C93D9B /* UpFamilyPlugin.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; path = UpFamilyPlugin.podspec; sourceTree = SOURCE_ROOT; };
		22EBB8B426D8D68100C93D9B /* CucumberRunner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CucumberRunner.m; sourceTree = "<group>"; };
		22EBB8BC26D8DDC000C93D9B /* UpFamilyDeclaration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFamilyDeclaration.h; sourceTree = "<group>"; };
		22EBB8BD26D8DDC000C93D9B /* UpFamilyDeclaration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyDeclaration.m; sourceTree = "<group>"; };
		22EBB8BF26D8DE8600C93D9B /* UpFamilyPluginManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFamilyPluginManager.h; sourceTree = "<group>"; };
		22EBB8C026D8DE8600C93D9B /* UpFamilyPluginManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyPluginManager.m; sourceTree = "<group>"; };
		22EBB8C226D8E07500C93D9B /* UpGetFamiliesAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpGetFamiliesAction.h; sourceTree = "<group>"; };
		22EBB8C326D8E07500C93D9B /* UpGetFamiliesAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpGetFamiliesAction.m; sourceTree = "<group>"; };
		2C29A5322817E88100236CF9 /* UpRemoveDeviceFromFamilyAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpRemoveDeviceFromFamilyAction.h; sourceTree = "<group>"; };
		2C29A5332817E88100236CF9 /* UpRemoveDeviceFromFamilyAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpRemoveDeviceFromFamilyAction.m; sourceTree = "<group>"; };
		393F867A0902F92C6F671DCE /* libPods-UpFamilyPlugin.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpFamilyPlugin.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		42E156175919D5F219C6AC4A /* libPods-UpFamilyPlugin-UpFamilyPluginTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpFamilyPlugin-UpFamilyPluginTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		6B042A2565AB1908F86C894F /* Pods-UpFamilyPlugin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpFamilyPlugin.debug.xcconfig"; path = "Target Support Files/Pods-UpFamilyPlugin/Pods-UpFamilyPlugin.debug.xcconfig"; sourceTree = "<group>"; };
		84F59F9A2ABD861E00E88D5A /* UpUpdateAndCheckDeviceNameAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUpdateAndCheckDeviceNameAction.h; sourceTree = "<group>"; };
		84F59F9B2ABD861E00E88D5A /* UpUpdateAndCheckDeviceNameAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUpdateAndCheckDeviceNameAction.m; sourceTree = "<group>"; };
		950119EC2DA9171800F92F77 /* UpModifyMemberTypeAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpModifyMemberTypeAction.h; sourceTree = "<group>"; };
		950119ED2DA9171800F92F77 /* UpModifyMemberTypeAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpModifyMemberTypeAction.m; sourceTree = "<group>"; };
		950119FA2DA92EA400F92F77 /* UpFamilyAddRoomNewAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFamilyAddRoomNewAction.h; sourceTree = "<group>"; };
		950119FB2DA92EA400F92F77 /* UpFamilyAddRoomNewAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyAddRoomNewAction.m; sourceTree = "<group>"; };
		95D74BF32D94E9D200086E12 /* UpModifyDeviceCardStatusAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpModifyDeviceCardStatusAction.h; sourceTree = "<group>"; };
		95D74BF42D94E9D200086E12 /* UpModifyDeviceCardStatusAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpModifyDeviceCardStatusAction.m; sourceTree = "<group>"; };
		95D74BF62D94EFE100086E12 /* UpModifyDeviceAggregationAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpModifyDeviceAggregationAction.h; sourceTree = "<group>"; };
		95D74BF72D94EFE100086E12 /* UpModifyDeviceAggregationAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpModifyDeviceAggregationAction.m; sourceTree = "<group>"; };
		95D74BF92D94F3E500086E12 /* UpModifyAggregationSwitchAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpModifyAggregationSwitchAction.h; sourceTree = "<group>"; };
		95D74BFA2D94F3E500086E12 /* UpModifyAggregationSwitchAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpModifyAggregationSwitchAction.m; sourceTree = "<group>"; };
		95EC2CB02DB6382A00E22D0E /* UpSaveRoomsOrderNewAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpSaveRoomsOrderNewAction.h; sourceTree = "<group>"; };
		95EC2CB12DB6382A00E22D0E /* UpSaveRoomsOrderNewAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpSaveRoomsOrderNewAction.m; sourceTree = "<group>"; };
		B8814001D5008750B1BAA6E1 /* Pods-UpFamilyPlugin-UpFamilyPluginTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpFamilyPlugin-UpFamilyPluginTests.release.xcconfig"; path = "Target Support Files/Pods-UpFamilyPlugin-UpFamilyPluginTests/Pods-UpFamilyPlugin-UpFamilyPluginTests.release.xcconfig"; sourceTree = "<group>"; };
		DEA67A546740EC1B4A15C094 /* Pods-UpFamilyPlugin-UpFamilyPluginTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpFamilyPlugin-UpFamilyPluginTests.debug.xcconfig"; path = "Target Support Files/Pods-UpFamilyPlugin-UpFamilyPluginTests/Pods-UpFamilyPlugin-UpFamilyPluginTests.debug.xcconfig"; sourceTree = "<group>"; };
		F7CBB36927964937007A713D /* UpQueryFirstMemberAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpQueryFirstMemberAction.h; sourceTree = "<group>"; };
		F7CBB36A27964937007A713D /* UpQueryFirstMemberAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpQueryFirstMemberAction.m; sourceTree = "<group>"; };
		F7CBB36C2796495D007A713D /* UpQueryFamilyInfoAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpQueryFamilyInfoAction.h; sourceTree = "<group>"; };
		F7CBB36D2796495D007A713D /* UpQueryFamilyInfoAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpQueryFamilyInfoAction.m; sourceTree = "<group>"; };
		F7CBB36F2796497F007A713D /* UpAddVirtualMemberAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAddVirtualMemberAction.h; sourceTree = "<group>"; };
		F7CBB3702796497F007A713D /* UpAddVirtualMemberAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAddVirtualMemberAction.m; sourceTree = "<group>"; };
		F7CBB372279649AA007A713D /* UpModifyVirtualMemberAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpModifyVirtualMemberAction.h; sourceTree = "<group>"; };
		F7CBB373279649AA007A713D /* UpModifyVirtualMemberAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpModifyVirtualMemberAction.m; sourceTree = "<group>"; };
		F7E55AB427B1FE7500121DC9 /* UpMoveDevicesToFamilyAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpMoveDevicesToFamilyAction.h; sourceTree = "<group>"; };
		F7E55AB527B1FE7500121DC9 /* UpMoveDevicesToFamilyAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpMoveDevicesToFamilyAction.m; sourceTree = "<group>"; };
		F7E55AB727B1FE8F00121DC9 /* UpUnbindDeviceFromFamilyAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUnbindDeviceFromFamilyAction.h; sourceTree = "<group>"; };
		F7E55AB827B1FE8F00121DC9 /* UpUnbindDeviceFromFamilyAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUnbindDeviceFromFamilyAction.m; sourceTree = "<group>"; };
		F7E55ABB27B206C300121DC9 /* BatchProcessDevice+UFPTools.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "BatchProcessDevice+UFPTools.h"; sourceTree = "<group>"; };
		F7E55ABC27B206C300121DC9 /* BatchProcessDevice+UFPTools.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "BatchProcessDevice+UFPTools.m"; sourceTree = "<group>"; };
		F8AEC6A42D826C51008C92C5 /* UpConfirmDeviceSharingRelationAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpConfirmDeviceSharingRelationAction.h; sourceTree = "<group>"; };
		F8AEC6A52D826C51008C92C5 /* UpConfirmDeviceSharingRelationAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpConfirmDeviceSharingRelationAction.m; sourceTree = "<group>"; };
		F8AEC6A72D8272A8008C92C5 /* UpCancelDeviceSharingRelationAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCancelDeviceSharingRelationAction.h; sourceTree = "<group>"; };
		F8AEC6A82D8272A8008C92C5 /* UpCancelDeviceSharingRelationAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCancelDeviceSharingRelationAction.m; sourceTree = "<group>"; };
		FE79FB3021890119D3CF6D42 /* Pods-UpFamilyPlugin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpFamilyPlugin.release.xcconfig"; path = "Target Support Files/Pods-UpFamilyPlugin/Pods-UpFamilyPlugin.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		22EBB87526D8CA7200C93D9B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				983DD52C84770B09B00A3C26 /* libPods-UpFamilyPlugin.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		22EBB88526D8CAB200C93D9B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		22EBB8A226D8CB1D00C93D9B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22EBB8AA26D8CB1D00C93D9B /* libUpFamilyPlugin.a in Frameworks */,
				12F9D2155A7C340D8FB841EB /* libPods-UpFamilyPlugin-UpFamilyPluginTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		224AA1A226E1B6B8005D552D /* CommonModel */ = {
			isa = PBXGroup;
			children = (
				224AA1A326E1B78E005D552D /* UpCommonFamilyModel.h */,
				224AA1A426E1B78E005D552D /* UpCommonFamilyModel.m */,
			);
			path = CommonModel;
			sourceTree = "<group>";
		};
		224AA1A626E5A5C0005D552D /* Utils */ = {
			isa = PBXGroup;
			children = (
				221019D026E6F731004D0295 /* NSString+NumberParser.h */,
				221019CF26E6F731004D0295 /* NSString+NumberParser.m */,
				224AA1B826E5AC35005D552D /* NSArray+Lite.h */,
				224AA1B726E5AC35005D552D /* NSArray+Lite.m */,
				224AA1B626E5AC35005D552D /* NSDictionary+SubMap.h */,
				224AA1B926E5AC35005D552D /* NSDictionary+SubMap.m */,
				224AA1B126E5ABBA005D552D /* StepsUtils.h */,
				224AA1B226E5ABBA005D552D /* StepsUtils.m */,
				224AA1AE26E5AAC5005D552D /* StepsCacheData.h */,
				224AA1AF26E5AAC5005D552D /* StepsCacheData.m */,
				224AA1BD26E5AC4C005D552D /* UPUnitTestCallBackIMP.h */,
				224AA1BE26E5AC4C005D552D /* UPUnitTestCallBackIMP.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		224AA1A726E5A5D4005D552D /* Steps */ = {
			isa = PBXGroup;
			children = (
				224AA1A926E5A606005D552D /* ActionSteps.h */,
				224AA1A826E5A606005D552D /* ActionSteps.m */,
				224AA1AB26E5A650005D552D /* InitSteps.h */,
				224AA1AC26E5A650005D552D /* InitSteps.m */,
			);
			path = Steps;
			sourceTree = "<group>";
		};
		225658D226DC6F690045BF18 /* Utils */ = {
			isa = PBXGroup;
			children = (
				225658D326DC6FBF0045BF18 /* UpFamilyPluginUtil.h */,
				225658D426DC6FBF0045BF18 /* UpFamilyPluginUtil.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		225658D626DCD92C0045BF18 /* Model */ = {
			isa = PBXGroup;
			children = (
				F7E55ABA27B2067500121DC9 /* Extentions */,
				224AA1A226E1B6B8005D552D /* CommonModel */,
				225658D826DDBADB0045BF18 /* H5Model */,
				225658D726DDBAC10045BF18 /* FlutterModel */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		225658D726DDBAC10045BF18 /* FlutterModel */ = {
			isa = PBXGroup;
			children = (
				225658D926DDBD5F0045BF18 /* UpFlutterFamilyModel.h */,
				225658DA26DDBD5F0045BF18 /* UpFlutterFamilyModel.m */,
			);
			path = FlutterModel;
			sourceTree = "<group>";
		};
		225658D826DDBADB0045BF18 /* H5Model */ = {
			isa = PBXGroup;
			children = (
				225658DC26DDBD7C0045BF18 /* UpH5FamilyModel.h */,
				225658DD26DDBD7C0045BF18 /* UpH5FamilyModel.m */,
			);
			path = H5Model;
			sourceTree = "<group>";
		};
		22EBB86F26D8CA7200C93D9B = {
			isa = PBXGroup;
			children = (
				22EBB8B026D8CEAD00C93D9B /* doc */,
				22EBB87A26D8CA7200C93D9B /* UpFamilyPlugin */,
				22EBB88926D8CAB200C93D9B /* UpFamilyPluginDebug */,
				22EBB8A626D8CB1D00C93D9B /* UpFamilyPluginTests */,
				22EBB87926D8CA7200C93D9B /* Products */,
				4AA0599D2AC6027B9ADB0023 /* Pods */,
				B701C5BAC831687FAFAC254E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		22EBB87926D8CA7200C93D9B /* Products */ = {
			isa = PBXGroup;
			children = (
				22EBB87826D8CA7200C93D9B /* libUpFamilyPlugin.a */,
				22EBB88826D8CAB200C93D9B /* UpFamilyPluginDebug.app */,
				22EBB8A526D8CB1D00C93D9B /* UpFamilyPluginTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		22EBB87A26D8CA7200C93D9B /* UpFamilyPlugin */ = {
			isa = PBXGroup;
			children = (
				225658D626DCD92C0045BF18 /* Model */,
				225658D226DC6F690045BF18 /* Utils */,
				22EBB8BB26D8D74F00C93D9B /* Defines */,
				22EBB8BA26D8D73A00C93D9B /* PluginManager */,
				22EBB8B926D8D72C00C93D9B /* Action */,
			);
			path = UpFamilyPlugin;
			sourceTree = "<group>";
		};
		22EBB88926D8CAB200C93D9B /* UpFamilyPluginDebug */ = {
			isa = PBXGroup;
			children = (
				22EBB88A26D8CAB200C93D9B /* AppDelegate.h */,
				22EBB88B26D8CAB200C93D9B /* AppDelegate.m */,
				22EBB88D26D8CAB200C93D9B /* SceneDelegate.h */,
				22EBB88E26D8CAB200C93D9B /* SceneDelegate.m */,
				22EBB89026D8CAB200C93D9B /* ViewController.h */,
				22EBB89126D8CAB200C93D9B /* ViewController.m */,
				22EBB89326D8CAB200C93D9B /* Main.storyboard */,
				22EBB89626D8CAB300C93D9B /* Assets.xcassets */,
				22EBB89826D8CAB300C93D9B /* LaunchScreen.storyboard */,
				22EBB89B26D8CAB300C93D9B /* Info.plist */,
				22EBB89C26D8CAB300C93D9B /* main.m */,
			);
			path = UpFamilyPluginDebug;
			sourceTree = "<group>";
		};
		22EBB8A626D8CB1D00C93D9B /* UpFamilyPluginTests */ = {
			isa = PBXGroup;
			children = (
				224AA1C026E5AE6F005D552D /* features */,
				224AA1A726E5A5D4005D552D /* Steps */,
				224AA1A626E5A5C0005D552D /* Utils */,
				22EBB8B426D8D68100C93D9B /* CucumberRunner.m */,
				22157D0226E9D66000311498 /* UpPluginFamilyTestHolder.h */,
				22157D0326E9D66000311498 /* UpPluginFamilyTestHolder.m */,
				22EBB8A726D8CB1D00C93D9B /* UpFamilyPluginTests.m */,
				22EBB8A926D8CB1D00C93D9B /* Info.plist */,
			);
			path = UpFamilyPluginTests;
			sourceTree = "<group>";
		};
		22EBB8B026D8CEAD00C93D9B /* doc */ = {
			isa = PBXGroup;
			children = (
				22EBB8B326D8CECE00C93D9B /* UpFamilyPlugin.podspec */,
				22EBB8B126D8CEAD00C93D9B /* readme.md */,
				22EBB8B226D8CEAD00C93D9B /* release.md */,
			);
			path = doc;
			sourceTree = "<group>";
		};
		22EBB8B926D8D72C00C93D9B /* Action */ = {
			isa = PBXGroup;
			children = (
				22EBB8C226D8E07500C93D9B /* UpGetFamiliesAction.h */,
				22EBB8C326D8E07500C93D9B /* UpGetFamiliesAction.m */,
				225658C326DC6AA50045BF18 /* UpEditFamilyFloorAction.h */,
				225658C426DC6AA50045BF18 /* UpEditFamilyFloorAction.m */,
				225658C626DC6AD80045BF18 /* UpAdminInviteMemberAction.h */,
				225658C726DC6AD80045BF18 /* UpAdminInviteMemberAction.m */,
				225658C926DC6AFC0045BF18 /* UpDeleteFamilyFloorAction.h */,
				225658CA26DC6AFC0045BF18 /* UpDeleteFamilyFloorAction.m */,
				225658CC26DC6B270045BF18 /* UpCreateFamilyFloorAction.h */,
				225658CD26DC6B270045BF18 /* UpCreateFamilyFloorAction.m */,
				225658CF26DC6B4E0045BF18 /* UpFamilyAddRoomAction.h */,
				225658D026DC6B4E0045BF18 /* UpFamilyAddRoomAction.m */,
				950119FA2DA92EA400F92F77 /* UpFamilyAddRoomNewAction.h */,
				950119FB2DA92EA400F92F77 /* UpFamilyAddRoomNewAction.m */,
				22C9610C26DDFAD1001A4CA7 /* UpGetCurrentFamilyAction.h */,
				22C9610D26DDFAD1001A4CA7 /* UpGetCurrentFamilyAction.m */,
				22C9610F26DDFB02001A4CA7 /* UpSetCurrentFamilyAction.h */,
				22C9611026DDFB02001A4CA7 /* UpSetCurrentFamilyAction.m */,
				22C9611226DDFB1E001A4CA7 /* UpCreateFamilyAction.h */,
				22C9611326DDFB1E001A4CA7 /* UpCreateFamilyAction.m */,
				22C9611526DDFE4B001A4CA7 /* UpUpdateFamilyInfoAction.h */,
				22C9611626DDFE4B001A4CA7 /* UpUpdateFamilyInfoAction.m */,
				22C9611826DDFE86001A4CA7 /* UpChangeFamilyAdminAction.h */,
				22C9611926DDFE86001A4CA7 /* UpChangeFamilyAdminAction.m */,
				22C9611B26DE0062001A4CA7 /* UpExitFamilyAsMemberAction.h */,
				22C9611C26DE0062001A4CA7 /* UpExitFamilyAsMemberAction.m */,
				22C9611E26DE0098001A4CA7 /* UpExitFamilyAsAdminAction.h */,
				22C9611F26DE0098001A4CA7 /* UpExitFamilyAsAdminAction.m */,
				22C9612126DE00C6001A4CA7 /* UpDeleteFamilyAsAdminAction.h */,
				22C9612226DE00C6001A4CA7 /* UpDeleteFamilyAsAdminAction.m */,
				22C9612426DE0102001A4CA7 /* UpUpdateDeviceNameAction.h */,
				22C9612526DE0102001A4CA7 /* UpUpdateDeviceNameAction.m */,
				84F59F9A2ABD861E00E88D5A /* UpUpdateAndCheckDeviceNameAction.h */,
				84F59F9B2ABD861E00E88D5A /* UpUpdateAndCheckDeviceNameAction.m */,
				22C9612726DE02CF001A4CA7 /* UpRefreshRoomListAction.h */,
				22C9612826DE02CF001A4CA7 /* UpRefreshRoomListAction.m */,
				22C9612A26DE02FE001A4CA7 /* UpFamilyRemoveRoomAction.h */,
				22C9612B26DE02FE001A4CA7 /* UpFamilyRemoveRoomAction.m */,
				22C9612D26DE0343001A4CA7 /* UpUpdateRoomNameAction.h */,
				22C9612E26DE0343001A4CA7 /* UpUpdateRoomNameAction.m */,
				22C9613026DE0377001A4CA7 /* UpMoveDevicesToOtherRoomAction.h */,
				22C9613126DE0377001A4CA7 /* UpMoveDevicesToOtherRoomAction.m */,
				22C9613326DE03A6001A4CA7 /* UpAdminDeleteMemberAction.h */,
				22C9613426DE03A6001A4CA7 /* UpAdminDeleteMemberAction.m */,
				F7CBB36F2796497F007A713D /* UpAddVirtualMemberAction.h */,
				F7CBB3702796497F007A713D /* UpAddVirtualMemberAction.m */,
				F7CBB36927964937007A713D /* UpQueryFirstMemberAction.h */,
				F7CBB36A27964937007A713D /* UpQueryFirstMemberAction.m */,
				F7CBB36C2796495D007A713D /* UpQueryFamilyInfoAction.h */,
				F7CBB36D2796495D007A713D /* UpQueryFamilyInfoAction.m */,
				F7CBB372279649AA007A713D /* UpModifyVirtualMemberAction.h */,
				F7CBB373279649AA007A713D /* UpModifyVirtualMemberAction.m */,
				2252139E28A2522F001D2832 /* UpModifyMemberRoleAction.h */,
				2252139F28A2522F001D2832 /* UpModifyMemberRoleAction.m */,
				950119EC2DA9171800F92F77 /* UpModifyMemberTypeAction.h */,
				950119ED2DA9171800F92F77 /* UpModifyMemberTypeAction.m */,
				223223F328B5FE9D00A7A0E7 /* UpModifyVirtualMemberRoleAction.h */,
				223223F428B5FE9D00A7A0E7 /* UpModifyVirtualMemberRoleAction.m */,
				F7E55AB427B1FE7500121DC9 /* UpMoveDevicesToFamilyAction.h */,
				F7E55AB527B1FE7500121DC9 /* UpMoveDevicesToFamilyAction.m */,
				F7E55AB727B1FE8F00121DC9 /* UpUnbindDeviceFromFamilyAction.h */,
				F7E55AB827B1FE8F00121DC9 /* UpUnbindDeviceFromFamilyAction.m */,
				2C29A5322817E88100236CF9 /* UpRemoveDeviceFromFamilyAction.h */,
				2C29A5332817E88100236CF9 /* UpRemoveDeviceFromFamilyAction.m */,
				22189C5C292239D40086193F /* UpSaveRoomsOrderAction.h */,
				22189C5D292239D40086193F /* UpSaveRoomsOrderAction.m */,
				95EC2CB02DB6382A00E22D0E /* UpSaveRoomsOrderNewAction.h */,
				95EC2CB12DB6382A00E22D0E /* UpSaveRoomsOrderNewAction.m */,
				F8AEC6A42D826C51008C92C5 /* UpConfirmDeviceSharingRelationAction.h */,
				F8AEC6A52D826C51008C92C5 /* UpConfirmDeviceSharingRelationAction.m */,
				F8AEC6A72D8272A8008C92C5 /* UpCancelDeviceSharingRelationAction.h */,
				F8AEC6A82D8272A8008C92C5 /* UpCancelDeviceSharingRelationAction.m */,
				95D74BF32D94E9D200086E12 /* UpModifyDeviceCardStatusAction.h */,
				95D74BF42D94E9D200086E12 /* UpModifyDeviceCardStatusAction.m */,
				95D74BF62D94EFE100086E12 /* UpModifyDeviceAggregationAction.h */,
				95D74BF72D94EFE100086E12 /* UpModifyDeviceAggregationAction.m */,
				95D74BF92D94F3E500086E12 /* UpModifyAggregationSwitchAction.h */,
				95D74BFA2D94F3E500086E12 /* UpModifyAggregationSwitchAction.m */,
			);
			path = Action;
			sourceTree = "<group>";
		};
		22EBB8BA26D8D73A00C93D9B /* PluginManager */ = {
			isa = PBXGroup;
			children = (
				22EBB8BF26D8DE8600C93D9B /* UpFamilyPluginManager.h */,
				22EBB8C026D8DE8600C93D9B /* UpFamilyPluginManager.m */,
			);
			path = PluginManager;
			sourceTree = "<group>";
		};
		22EBB8BB26D8D74F00C93D9B /* Defines */ = {
			isa = PBXGroup;
			children = (
				22EBB8BC26D8DDC000C93D9B /* UpFamilyDeclaration.h */,
				22EBB8BD26D8DDC000C93D9B /* UpFamilyDeclaration.m */,
			);
			path = Defines;
			sourceTree = "<group>";
		};
		4AA0599D2AC6027B9ADB0023 /* Pods */ = {
			isa = PBXGroup;
			children = (
				6B042A2565AB1908F86C894F /* Pods-UpFamilyPlugin.debug.xcconfig */,
				FE79FB3021890119D3CF6D42 /* Pods-UpFamilyPlugin.release.xcconfig */,
				DEA67A546740EC1B4A15C094 /* Pods-UpFamilyPlugin-UpFamilyPluginTests.debug.xcconfig */,
				B8814001D5008750B1BAA6E1 /* Pods-UpFamilyPlugin-UpFamilyPluginTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		B701C5BAC831687FAFAC254E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				393F867A0902F92C6F671DCE /* libPods-UpFamilyPlugin.a */,
				42E156175919D5F219C6AC4A /* libPods-UpFamilyPlugin-UpFamilyPluginTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F7E55ABA27B2067500121DC9 /* Extentions */ = {
			isa = PBXGroup;
			children = (
				F7E55ABB27B206C300121DC9 /* BatchProcessDevice+UFPTools.h */,
				F7E55ABC27B206C300121DC9 /* BatchProcessDevice+UFPTools.m */,
			);
			path = Extentions;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		22EBB87726D8CA7200C93D9B /* UpFamilyPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22EBB88126D8CA7200C93D9B /* Build configuration list for PBXNativeTarget "UpFamilyPlugin" */;
			buildPhases = (
				35D17CE38FC9922AD9A01F44 /* [CP] Check Pods Manifest.lock */,
				22EBB87426D8CA7200C93D9B /* Sources */,
				22EBB87526D8CA7200C93D9B /* Frameworks */,
				22EBB87626D8CA7200C93D9B /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UpFamilyPlugin;
			productName = UpFamilyPlugin;
			productReference = 22EBB87826D8CA7200C93D9B /* libUpFamilyPlugin.a */;
			productType = "com.apple.product-type.library.static";
		};
		22EBB88726D8CAB200C93D9B /* UpFamilyPluginDebug */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22EBB8A026D8CAB300C93D9B /* Build configuration list for PBXNativeTarget "UpFamilyPluginDebug" */;
			buildPhases = (
				22EBB88426D8CAB200C93D9B /* Sources */,
				22EBB88526D8CAB200C93D9B /* Frameworks */,
				22EBB88626D8CAB200C93D9B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UpFamilyPluginDebug;
			productName = UpFamilyPluginDebug;
			productReference = 22EBB88826D8CAB200C93D9B /* UpFamilyPluginDebug.app */;
			productType = "com.apple.product-type.application";
		};
		22EBB8A426D8CB1D00C93D9B /* UpFamilyPluginTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22EBB8AD26D8CB1D00C93D9B /* Build configuration list for PBXNativeTarget "UpFamilyPluginTests" */;
			buildPhases = (
				B9702D2E65E6398E94F0AB72 /* [CP] Check Pods Manifest.lock */,
				22EBB8A126D8CB1D00C93D9B /* Sources */,
				22EBB8A226D8CB1D00C93D9B /* Frameworks */,
				22EBB8A326D8CB1D00C93D9B /* Resources */,
				FA4F243B8B91BF8F32030659 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				22EBB8AC26D8CB1D00C93D9B /* PBXTargetDependency */,
			);
			name = UpFamilyPluginTests;
			productName = UpFamilyPluginTests;
			productReference = 22EBB8A526D8CB1D00C93D9B /* UpFamilyPluginTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		22EBB87026D8CA7200C93D9B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1250;
				TargetAttributes = {
					22EBB87726D8CA7200C93D9B = {
						CreatedOnToolsVersion = 12.5;
					};
					22EBB88726D8CAB200C93D9B = {
						CreatedOnToolsVersion = 12.5;
					};
					22EBB8A426D8CB1D00C93D9B = {
						CreatedOnToolsVersion = 12.5;
					};
				};
			};
			buildConfigurationList = 22EBB87326D8CA7200C93D9B /* Build configuration list for PBXProject "UpFamilyPlugin" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 22EBB86F26D8CA7200C93D9B;
			productRefGroup = 22EBB87926D8CA7200C93D9B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				22EBB87726D8CA7200C93D9B /* UpFamilyPlugin */,
				22EBB88726D8CAB200C93D9B /* UpFamilyPluginDebug */,
				22EBB8A426D8CB1D00C93D9B /* UpFamilyPluginTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		22EBB88626D8CAB200C93D9B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22EBB89A26D8CAB300C93D9B /* LaunchScreen.storyboard in Resources */,
				22EBB89726D8CAB300C93D9B /* Assets.xcassets in Resources */,
				22EBB89526D8CAB200C93D9B /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		22EBB8A326D8CB1D00C93D9B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				224AA1C126E5AE6F005D552D /* features in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		35D17CE38FC9922AD9A01F44 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpFamilyPlugin-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B9702D2E65E6398E94F0AB72 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpFamilyPlugin-UpFamilyPluginTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FA4F243B8B91BF8F32030659 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpFamilyPlugin-UpFamilyPluginTests/Pods-UpFamilyPlugin-UpFamilyPluginTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpFamilyPlugin-UpFamilyPluginTests/Pods-UpFamilyPlugin-UpFamilyPluginTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UpFamilyPlugin-UpFamilyPluginTests/Pods-UpFamilyPlugin-UpFamilyPluginTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		22EBB87426D8CA7200C93D9B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22EBB8C126D8DE8600C93D9B /* UpFamilyPluginManager.m in Sources */,
				22189C5E292239D40086193F /* UpSaveRoomsOrderAction.m in Sources */,
				22C9612326DE00C6001A4CA7 /* UpDeleteFamilyAsAdminAction.m in Sources */,
				225658DE26DDBD7C0045BF18 /* UpH5FamilyModel.m in Sources */,
				22C9611726DDFE4B001A4CA7 /* UpUpdateFamilyInfoAction.m in Sources */,
				223223F528B5FE9D00A7A0E7 /* UpModifyVirtualMemberRoleAction.m in Sources */,
				95EC2CB22DB6382A00E22D0E /* UpSaveRoomsOrderNewAction.m in Sources */,
				22C9613226DE0377001A4CA7 /* UpMoveDevicesToOtherRoomAction.m in Sources */,
				F8AEC6A62D826C51008C92C5 /* UpConfirmDeviceSharingRelationAction.m in Sources */,
				84F59F9C2ABD861E00E88D5A /* UpUpdateAndCheckDeviceNameAction.m in Sources */,
				95D74BFB2D94F3E500086E12 /* UpModifyAggregationSwitchAction.m in Sources */,
				950119EE2DA9171800F92F77 /* UpModifyMemberTypeAction.m in Sources */,
				F7CBB3712796497F007A713D /* UpAddVirtualMemberAction.m in Sources */,
				225658CE26DC6B270045BF18 /* UpCreateFamilyFloorAction.m in Sources */,
				95D74BF52D94E9D200086E12 /* UpModifyDeviceCardStatusAction.m in Sources */,
				224AA1A526E1B78E005D552D /* UpCommonFamilyModel.m in Sources */,
				F7E55AB927B1FE8F00121DC9 /* UpUnbindDeviceFromFamilyAction.m in Sources */,
				F7CBB36E2796495D007A713D /* UpQueryFamilyInfoAction.m in Sources */,
				F8AEC6A92D8272A8008C92C5 /* UpCancelDeviceSharingRelationAction.m in Sources */,
				22C9612C26DE02FE001A4CA7 /* UpFamilyRemoveRoomAction.m in Sources */,
				95D74BF82D94EFE100086E12 /* UpModifyDeviceAggregationAction.m in Sources */,
				225658C526DC6AA50045BF18 /* UpEditFamilyFloorAction.m in Sources */,
				2C29A5342817E88100236CF9 /* UpRemoveDeviceFromFamilyAction.m in Sources */,
				22C9612F26DE0343001A4CA7 /* UpUpdateRoomNameAction.m in Sources */,
				225658CB26DC6AFC0045BF18 /* UpDeleteFamilyFloorAction.m in Sources */,
				F7CBB36B27964937007A713D /* UpQueryFirstMemberAction.m in Sources */,
				F7CBB374279649AA007A713D /* UpModifyVirtualMemberAction.m in Sources */,
				22C9611D26DE0062001A4CA7 /* UpExitFamilyAsMemberAction.m in Sources */,
				225658D126DC6B4E0045BF18 /* UpFamilyAddRoomAction.m in Sources */,
				22C9611A26DDFE86001A4CA7 /* UpChangeFamilyAdminAction.m in Sources */,
				22C9610E26DDFAD1001A4CA7 /* UpGetCurrentFamilyAction.m in Sources */,
				225658D526DC6FBF0045BF18 /* UpFamilyPluginUtil.m in Sources */,
				225213A028A2522F001D2832 /* UpModifyMemberRoleAction.m in Sources */,
				225658C826DC6AD80045BF18 /* UpAdminInviteMemberAction.m in Sources */,
				22EBB8C426D8E07500C93D9B /* UpGetFamiliesAction.m in Sources */,
				22C9613526DE03A6001A4CA7 /* UpAdminDeleteMemberAction.m in Sources */,
				225658DB26DDBD5F0045BF18 /* UpFlutterFamilyModel.m in Sources */,
				22C9612026DE0098001A4CA7 /* UpExitFamilyAsAdminAction.m in Sources */,
				950119FC2DA92EA400F92F77 /* UpFamilyAddRoomNewAction.m in Sources */,
				22C9612626DE0102001A4CA7 /* UpUpdateDeviceNameAction.m in Sources */,
				22EBB8BE26D8DDC000C93D9B /* UpFamilyDeclaration.m in Sources */,
				F7E55AB627B1FE7500121DC9 /* UpMoveDevicesToFamilyAction.m in Sources */,
				F7E55ABD27B206C300121DC9 /* BatchProcessDevice+UFPTools.m in Sources */,
				22C9611426DDFB1E001A4CA7 /* UpCreateFamilyAction.m in Sources */,
				22C9611126DDFB02001A4CA7 /* UpSetCurrentFamilyAction.m in Sources */,
				22C9612926DE02CF001A4CA7 /* UpRefreshRoomListAction.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		22EBB88426D8CAB200C93D9B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22EBB89226D8CAB200C93D9B /* ViewController.m in Sources */,
				22EBB88C26D8CAB200C93D9B /* AppDelegate.m in Sources */,
				22EBB89D26D8CAB300C93D9B /* main.m in Sources */,
				22EBB88F26D8CAB200C93D9B /* SceneDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		22EBB8A126D8CB1D00C93D9B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22157D0426E9D66000311498 /* UpPluginFamilyTestHolder.m in Sources */,
				224AA1AA26E5A607005D552D /* ActionSteps.m in Sources */,
				224AA1AD26E5A650005D552D /* InitSteps.m in Sources */,
				224AA1BB26E5AC35005D552D /* NSArray+Lite.m in Sources */,
				224AA1B026E5AAC5005D552D /* StepsCacheData.m in Sources */,
				224AA1BF26E5AC4C005D552D /* UPUnitTestCallBackIMP.m in Sources */,
				221019D126E6F731004D0295 /* NSString+NumberParser.m in Sources */,
				224AA1B326E5ABBA005D552D /* StepsUtils.m in Sources */,
				22EBB8A826D8CB1D00C93D9B /* UpFamilyPluginTests.m in Sources */,
				22EBB8B526D8D68100C93D9B /* CucumberRunner.m in Sources */,
				224AA1BC26E5AC35005D552D /* NSDictionary+SubMap.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		22EBB8AC26D8CB1D00C93D9B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 22EBB87726D8CA7200C93D9B /* UpFamilyPlugin */;
			targetProxy = 22EBB8AB26D8CB1D00C93D9B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		22EBB89326D8CAB200C93D9B /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				22EBB89426D8CAB200C93D9B /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		22EBB89826D8CAB300C93D9B /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				22EBB89926D8CAB300C93D9B /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		22EBB87F26D8CA7200C93D9B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		22EBB88026D8CA7200C93D9B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		22EBB88226D8CA7200C93D9B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6B042A2565AB1908F86C894F /* Pods-UpFamilyPlugin.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		22EBB88326D8CA7200C93D9B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FE79FB3021890119D3CF6D42 /* Pods-UpFamilyPlugin.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		22EBB89E26D8CAB300C93D9B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = UpFamilyPluginDebug/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.UpFamilyPluginDebug;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		22EBB89F26D8CAB300C93D9B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = UpFamilyPluginDebug/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.UpFamilyPluginDebug;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		22EBB8AE26D8CB1D00C93D9B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DEA67A546740EC1B4A15C094 /* Pods-UpFamilyPlugin-UpFamilyPluginTests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = UpFamilyPluginTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.UpFamilyPluginTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "";
			};
			name = Debug;
		};
		22EBB8AF26D8CB1D00C93D9B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B8814001D5008750B1BAA6E1 /* Pods-UpFamilyPlugin-UpFamilyPluginTests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = UpFamilyPluginTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.UpFamilyPluginTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		22EBB87326D8CA7200C93D9B /* Build configuration list for PBXProject "UpFamilyPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22EBB87F26D8CA7200C93D9B /* Debug */,
				22EBB88026D8CA7200C93D9B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22EBB88126D8CA7200C93D9B /* Build configuration list for PBXNativeTarget "UpFamilyPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22EBB88226D8CA7200C93D9B /* Debug */,
				22EBB88326D8CA7200C93D9B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22EBB8A026D8CAB300C93D9B /* Build configuration list for PBXNativeTarget "UpFamilyPluginDebug" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22EBB89E26D8CAB300C93D9B /* Debug */,
				22EBB89F26D8CAB300C93D9B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22EBB8AD26D8CB1D00C93D9B /* Build configuration list for PBXNativeTarget "UpFamilyPluginTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22EBB8AE26D8CB1D00C93D9B /* Debug */,
				22EBB8AF26D8CB1D00C93D9B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 22EBB87026D8CA7200C93D9B /* Project object */;
}
