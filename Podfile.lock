PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Cucumberish (1.4.0)
  - MJExtension (3.2.1)
  - OCMock (3.8.1)
  - Protobuf (3.17.0)
  - uplog (1.5.1):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.17.0)
    - ZipArchive (>= 1.4.0)
  - upnetwork (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= 4.0.5)
    - upnetwork/Headers (= 4.0.5)
    - upnetwork/HTTPDns (= 4.0.5)
    - upnetwork/Manager (= 4.0.5)
    - upnetwork/Request (= 4.0.5)
    - upnetwork/Settings (= 4.0.5)
    - upnetwork/Utils (= 4.0.5)
  - upnetwork/DynamicSign (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= 4.0.5)
  - upnetwork/DynamicSign/Private (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= 4.0.5)
  - upnetwork/Headers/Private (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= 4.0.5)
  - upnetwork/Manager/Private (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= 4.0.5)
  - upnetwork/Request/Private (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= 4.0.5)
  - upnetwork/Settings/Private (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= 4.0.5)
  - upnetwork/Utils/Private (4.0.5):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - UPPluginBaseAPI (0.1.3):
    - UPPluginBaseAPI/UPPluginBaseAPI (= 0.1.3)
  - UPPluginBaseAPI/UPPluginBaseAPI (0.1.3)
  - UpPluginFoundation (********.2023020301):
    - MJExtension
    - uplog (>= 1.1.22)
    - UPPluginBaseAPI (>= 0.1.0)
  - upuserdomain (999.999.999.2025042101):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
    - upuserdomain/upuserdomain (= 999.999.999.2025042101)
    - upuserdomain/UserDomainAPIs (= 999.999.999.2025042101)
    - upuserdomain/UserDomainDataSource (= 999.999.999.2025042101)
  - upuserdomain/upuserdomain (999.999.999.2025042101):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainAPIs (999.999.999.2025042101):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainDataSource (999.999.999.2025042101):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - Cucumberish (= 1.4.0)
  - MJExtension (= 3.2.1)
  - OCMock (= 3.8.1)
  - uplog (= 1.5.1)
  - upnetwork (= 4.0.5)
  - UPPluginBaseAPI (= 0.1.3)
  - UpPluginFoundation (= ********.2023020301)
  - upuserdomain (= 999.999.999.2025042101)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - uplog
    - upnetwork
    - UPPluginBaseAPI
    - UpPluginFoundation
    - upuserdomain
  trunk:
    - AFNetworking
    - Cucumberish
    - MJExtension
    - OCMock
    - Protobuf
    - ZipArchive

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Cucumberish: 6cbd0c1f50306b369acebfe7d9f514c9c287d26c
  MJExtension: 635f2c663dcb1bf76fa4b715b2570a5710aec545
  OCMock: 29f6e52085b4e7d9b075cbf03ed7c3112f82f934
  Protobuf: 7327d4444215b5f18e560a97f879ff5503c4581c
  uplog: 7774a64dab5e5178282819e1e63ad57ae415e805
  upnetwork: 37c14d657a9250c802111e9b0ae6d2ece1bffc46
  UPPluginBaseAPI: 0aca03a1616948abc0d0000dea46b6ee1b4d3d57
  UpPluginFoundation: 51455f69d894ea83df4f60948c739b0933be8932
  upuserdomain: caeb82b753385390584758069117b3a31725b162
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: 297cf73e230e82480e6d03c4f87617f237acb278

COCOAPODS: 1.12.1
